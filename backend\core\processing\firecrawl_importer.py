import os
import json
import requests
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Try to import settings from the API config
try:
    from api.core.config import settings
except ImportError:
    # Fallback if import fails
    logger.warning("Could not import settings from api.core.config")
    logger.warning("Using environment variables directly (not recommended)")
    from dotenv import load_dotenv
    load_dotenv()

    class Settings:
        FIRECRAWL_API_KEY = os.getenv("FIRECRAWL_API_KEY")

    settings = Settings()

# Firecrawl API configuration
FIRECRAWL_API_KEY = settings.FIRECRAWL_API_KEY
FIRECRAWL_API_URL = "https://api.firecrawl.dev"

def scrape_url_with_firecrawl(url: str) -> Dict:
    """
    Scrape a URL using Firecrawl API
    
    Args:
        url: URL to scrape
        
    Returns:
        Dict: Scraped data
    """
    try:
        logger.info(f"Scraping URL with Firecrawl: {url}")
        
        # Check if API key is available
        if not FIRECRAWL_API_KEY:
            logger.error("Firecrawl API key not found")
            return {"error": "Firecrawl API key not found"}
        
        # Headers with authentication
        headers = {
            "accept": "application/json",
            "x-api-key": FIRECRAWL_API_KEY,
            "Content-Type": "application/json"
        }
        
        # Create payload for scraping request
        payload = {
            "url": url,
            "extract_type": "job_posting",  # Specify that we're extracting job postings
            "options": {
                "wait_for": ".job-listing, .job-post, .job-card, article, .vacancy",  # Common job listing selectors
                "timeout": 30000  # 30 seconds timeout
            }
        }
        
        # Make request to Firecrawl API
        response = requests.post(
            f"{FIRECRAWL_API_URL}/v1/extract",
            headers=headers,
            json=payload
        )
        
        # Print response details for debugging
        logger.debug(f"Response status: {response.status_code}")
        logger.debug(f"Response headers: {dict(response.headers)}")
        
        # Check for specific error cases
        if response.status_code == 404:
            logger.error(f"Error: No data found for URL {url}")
            return {"error": "No data found"}
        elif response.status_code == 401:
            logger.error("Error: Invalid or expired API key")
            return {"error": "Invalid API key"}
        elif response.status_code == 403:
            logger.error("Error: Access forbidden")
            return {"error": "Access forbidden"}
        
        # Raise for other error status codes
        response.raise_for_status()
        
        # Parse response
        data = response.json()
        
        logger.info(f"Successfully scraped URL: {url}")
        return data
    except Exception as e:
        logger.error(f"Error scraping URL {url}: {str(e)}")
        return {"error": str(e)}

def process_firecrawl_jobs(scraped_data: Dict, prefix_index: str) -> Dict[str, Dict]:
    """
    Process job data from Firecrawl and format it for storage
    
    Args:
        scraped_data: Raw data from Firecrawl API
        prefix_index: Three-letter prefix for job IDs
        
    Returns:
        Dict[str, Dict]: Dictionary of processed jobs with job IDs as keys
    """
    try:
        logger.info(f"Processing Firecrawl jobs with prefix: {prefix_index}")
        
        # Check for errors in scraped data
        if "error" in scraped_data:
            logger.error(f"Error in scraped data: {scraped_data['error']}")
            return {}
        
        # Extract job listings from scraped data
        job_listings = scraped_data.get("data", {}).get("job_postings", [])
        
        if not job_listings:
            logger.warning(f"No job listings found in scraped data")
            return {}
        
        logger.info(f"Found {len(job_listings)} job listings")
        
        # Process each job listing
        jobs_dict = {}
        for i, job in enumerate(job_listings, 1):
            job_id = f"{prefix_index}{i}"
            
            # Extract job details
            title = job.get("title", "Unknown Position")
            company = job.get("company", "")
            location = job.get("location", "")
            description = job.get("description", "")
            url = job.get("url", "")
            
            # Create processed job object
            jobs_dict[job_id] = {
                "prefixindex": prefix_index,
                "jobtitle": title,
                "location": location,
                "company": company,
                "shortlist": {"is_shortlisted": False, "score": 0, "timestamp": None},
                "evaluation": {"is_evaluated": False, "score": 0, "pros": "", "cons": "", "timestamp": None},
                "display": {
                    "color": "none",
                    "status": "none",
                    "text": f"{job_id}: {title}"
                },
                "job_description": description,
                "link": url,
                "source": "Firecrawl",
                "scraped_at": datetime.now().isoformat()
            }
        
        logger.info(f"Successfully processed {len(jobs_dict)} jobs")
        return jobs_dict
    except Exception as e:
        logger.error(f"Error processing Firecrawl jobs: {str(e)}")
        return {}

def import_jobs_from_urls(urls: List[str]) -> Dict[str, Dict]:
    """
    Import jobs from a list of URLs using Firecrawl
    
    Args:
        urls: List of URLs to scrape
        
    Returns:
        Dict[str, Dict]: Dictionary of all jobs with job IDs as keys
    """
    try:
        logger.info(f"Importing jobs from {len(urls)} URLs")
        
        all_jobs = {}
        
        for idx, url in enumerate(urls):
            try:
                # Generate prefix for this URL
                prefix = generate_prefix_from_id(idx)
                
                logger.info(f"Processing URL {idx+1}/{len(urls)}: {url} with prefix {prefix}")
                
                # Scrape URL
                scraped_data = scrape_url_with_firecrawl(url)
                
                # Process job data
                jobs = process_firecrawl_jobs(scraped_data, prefix)
                
                if jobs:
                    # Add jobs to all_jobs dictionary
                    all_jobs.update(jobs)
                    logger.info(f"Added {len(jobs)} jobs from {url}")
                else:
                    logger.warning(f"No jobs found at {url}")
            except Exception as e:
                logger.error(f"Error processing URL {url}: {str(e)}")
                # Continue with next URL
                continue
        
        logger.info(f"Successfully imported {len(all_jobs)} jobs from {len(urls)} URLs")
        return all_jobs
    except Exception as e:
        logger.error(f"Error importing jobs from URLs: {str(e)}")
        return {}

def generate_prefix_from_id(index: int) -> str:
    """
    Generate a three-letter prefix from an index (AAA, AAB, AAC, etc.)
    
    Args:
        index: Zero-based index
        
    Returns:
        str: Three-letter prefix
    """
    letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    first = letters[index // (26 * 26) % 26]
    second = letters[(index // 26) % 26]
    third = letters[index % 26]
    return f"{first}{second}{third}"

# Test function
if __name__ == "__main__":
    test_url = "https://careers.eoexecutives.com/"
    print(f"Testing with URL: {test_url}")
    
    scraped_data = scrape_url_with_firecrawl(test_url)
    jobs = process_firecrawl_jobs(scraped_data, "AAA")
    
    print(f"Scraped {len(jobs)} jobs")
    for job_id, job in jobs.items():
        print(f"{job_id}: {job['jobtitle']}")
