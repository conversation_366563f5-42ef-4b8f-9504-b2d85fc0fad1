import pandas as pd
import os
import importlib.util

def import_jobs(job_sites_path):
    """
    Import job postings from multiple sources and return them in a format
    suitable for GUI display.
    
    Args:
        job_sites_path: Path to the job_sites.xlsx file
    
    Returns:
        list: List of job titles formatted as "ID: Job Title" for GUI display
    """
    try:
        # Initialize the containers
        display_list = []
        
        # Read the job_sites.xlsx file
        sites_df = pd.read_excel(job_sites_path, sheet_name="URL")
        
        # Process each job source, but stop at first NaN row
        for idx, row in sites_df.iterrows():
            # Check if any essential field is NaN
            if pd.isna(row["ScraperFunction"]) or pd.isna(row["Data-File"]) or pd.isna(row["PrefixIndex"]):
                print(f"Found empty row at index {idx}, stopping processing")
                break
                
            try:
                # Get source information
                scraper_name = str(row["ScraperFunction"]).strip()
                data_file = str(row["Data-File"]).strip()
                prefix_index = str(row["PrefixIndex"]).strip()
                
                print(f"Processing source {prefix_index}: {data_file}")
                
                # Import the scraper function dynamically
                scraper_path = os.path.join(
                    os.path.dirname(os.path.dirname(__file__)), 
                    'url_scrapers', 
                    f"{scraper_name}.py"
                )
                
                if not os.path.exists(scraper_path):
                    print(f"❌ ERROR: Scraper file not found: {scraper_path}")
                    continue
                    
                spec = importlib.util.spec_from_file_location(scraper_name, scraper_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # Get the data file path
                data_file_path = os.path.join(
                    os.path.dirname(os.path.dirname(__file__)), 
                    'scraped_urls', 
                    data_file
                )
                
                if not os.path.exists(data_file_path):
                    print(f"❌ ERROR: Data file not found: {data_file_path}")
                    continue
                
                # Use the scraper function to read the data
                try:
                    # The scraper returns a list of jobs
                    jobs = module.scrape(data_file_path)
                    
                    # Format each job for display
                    for i, job in enumerate(jobs, 1):
                        if pd.isna(job['job_title']):  # Skip jobs with NaN titles
                            continue
                        job_id = f"{prefix_index}{i}"
                        display_list.append(f"{job_id}: {job['job_title']}")
                    
                    print(f"Successfully loaded data using {scraper_name}")
                    
                except Exception as e:
                    print(f"❌ ERROR using {scraper_name}: {str(e)}")
                    continue
                    
            except Exception as e:
                print(f"❌ ERROR processing source {row.get('URL', f'row {idx+1}')}: {str(e)}")
                continue
                
        return display_list
        
    except Exception as e:
        print(f"❌ ERROR importing jobs: {str(e)}")
        return []

# Enhanced test section
if __name__ == "__main__":
    print("🧪 Testing job_importer.py independently...")
    try:
        # Get the path to job_sites.xlsx
        test_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "job_sites.xlsx")
        print(f"📂 Using job_sites.xlsx at: {test_path}")
        
        # Import the jobs
        print("\n📥 Importing jobs...")
        job_titles = import_jobs(test_path)
        
        # Display results
        print("\n📋 Jobs found:")
        print("-" * 50)
        for job in job_titles:
            print(job)
        print("-" * 50)
        
        # Show summary
        print(f"\n📊 Summary:")
        print(f"Total jobs found: {len(job_titles)}")
        
        # Show prefixes found
        prefixes = set(job[0] for job in job_titles if job)  # Get unique prefixes
        print(f"Sources processed: {', '.join(sorted(prefixes))}")
                
    except Exception as e:
        print(f"❌ Test failed: {str(e)}") 