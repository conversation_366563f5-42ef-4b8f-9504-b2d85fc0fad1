'use client';

import React, { useState } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  Heading,
  Flex,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  SliderMark,
  Tooltip,
  HStack,
  useToast,
  Alert,
  AlertIcon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Radio,
  RadioGroup,
  Stack,
} from '@chakra-ui/react';
import { useEmbeddingStore } from '../stores/embeddingStore';
import { useEvaluationStore } from '../stores/evaluationStore';

interface ShortlistControlsProps {
  onShortlistComplete?: (results: any) => void;
  onEvaluationComplete?: (results: any) => void;
  disabled?: boolean;
  hasEmbedding?: boolean;
}

const ShortlistControls: React.FC<ShortlistControlsProps> = ({
  onShortlistComplete,
  onEvaluationComplete,
  disabled = true,
  hasEmbedding = false,
}) => {
  // State for controls
  const [temperature, setTemperature] = useState<number>(0.7);
  const [threshold, setThreshold] = useState<number>(70);
  const [showTempTooltip, setShowTempTooltip] = useState<boolean>(false);
  const [showThresholdTooltip, setShowThresholdTooltip] = useState<boolean>(false);
  const [isShortlisting, setIsShortlisting] = useState<boolean>(false);
  const [isEvaluating, setIsEvaluating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [shortlistResults, setShortlistResults] = useState<any>(null);

  // State for RAG shortlisting
  const [ragMethod, setRagMethod] = useState<string>('faiss_preselect');
  const {
    isOpen: isRagDialogOpen,
    onOpen: openRagDialog,
    onClose: closeRagDialog,
  } = useDisclosure();

  // Get FAISS threshold from embedding store
  const { faissThreshold } = useEmbeddingStore();

  // Get evaluation functions from evaluation store
  const { evaluateJobs } = useEvaluationStore();

  const toast = useToast();

  // Handle standard shortlisting
  const handleShortlist = async () => {
    setIsShortlisting(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:8000/api/v1/jobs/shortlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          temperature,
          threshold,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to shortlist jobs: ${response.status}`);
      }

      const results = await response.json();
      setShortlistResults(results);

      if (onShortlistComplete) {
        onShortlistComplete(results);
      }

      toast({
        title: 'Shortlisting Complete',
        description: `Found ${results.shortlisted_count || 0} matching jobs`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error shortlisting jobs:', err);
      setError(err instanceof Error ? err.message : 'Failed to shortlist jobs');

      toast({
        title: 'Shortlisting Failed',
        description: err instanceof Error ? err.message : 'Failed to shortlist jobs',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsShortlisting(false);
    }
  };

  // Handle RAG-based shortlisting
  const handleRagShortlist = async () => {
    closeRagDialog();
    setIsShortlisting(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:8000/api/v1/jobs/rag-shortlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method: ragMethod,
          faiss_threshold: faissThreshold,
          temperature,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to perform RAG shortlisting: ${response.status}`);
      }

      const results = await response.json();
      setShortlistResults(results);

      if (onShortlistComplete) {
        onShortlistComplete(results);
      }

      toast({
        title: 'RAG Shortlisting Complete',
        description: `Found ${results.shortlisted_count || 0} matching jobs using ${ragMethod === 'full_llm' ? 'Full LLM' : 'FAISS Pre-selection'}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error in RAG shortlisting:', err);
      setError(err instanceof Error ? err.message : 'Failed to perform RAG shortlisting');

      toast({
        title: 'RAG Shortlisting Failed',
        description: err instanceof Error ? err.message : 'Failed to perform RAG shortlisting',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsShortlisting(false);
    }
  };

  // Handle evaluation
  const handleEvaluate = async () => {
    if (!shortlistResults || !shortlistResults.shortlisted_jobs) {
      toast({
        title: 'No Jobs to Evaluate',
        description: 'Please shortlist jobs first',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsEvaluating(true);
    setError(null);

    try {
      // Get job IDs from shortlist results
      const jobIds = shortlistResults.shortlisted_jobs.map((job: any) => job.id);

      // Use the evaluation store to evaluate jobs
      const results = await evaluateJobs(jobIds);

      if (onEvaluationComplete) {
        onEvaluationComplete({ evaluated_jobs: results });
      }

      toast({
        title: 'Evaluation Complete',
        description: `Evaluated ${results.length} jobs`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error evaluating jobs:', err);
      setError(err instanceof Error ? err.message : 'Failed to evaluate jobs');

      toast({
        title: 'Evaluation Failed',
        description: err instanceof Error ? err.message : 'Failed to evaluate jobs',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsEvaluating(false);
    }
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="white">
      <VStack align="stretch" spacing={4}>
        <Heading size="md">Shortlist Controls</Heading>

        <Tabs variant="enclosed" colorScheme="blue">
          <TabList>
            <Tab>Standard</Tab>
            <Tab isDisabled={!hasEmbedding}>RAG-based</Tab>
          </TabList>

          <TabPanels>
            {/* Standard Shortlisting Panel */}
            <TabPanel p={4}>
              <VStack align="stretch" spacing={4}>
                <Box>
                  <Text mb={2}>Temperature: {temperature.toFixed(1)}</Text>
                  <Slider
                    id="temperature-slider"
                    min={0}
                    max={1}
                    step={0.1}
                    value={temperature}
                    onChange={val => setTemperature(val)}
                    onMouseEnter={() => setShowTempTooltip(true)}
                    onMouseLeave={() => setShowTempTooltip(false)}
                  >
                    <SliderTrack>
                      <SliderFilledTrack />
                    </SliderTrack>
                    <Tooltip
                      hasArrow
                      bg="blue.500"
                      color="white"
                      placement="top"
                      isOpen={showTempTooltip}
                      label={`${temperature.toFixed(1)}: ${temperature <= 0.3 ? 'More focused' : temperature >= 0.8 ? 'More creative' : 'Balanced'}`}
                    >
                      <SliderThumb />
                    </Tooltip>
                  </Slider>
                </Box>

                <Box>
                  <Text mb={2}>Match Threshold: {threshold}%</Text>
                  <Slider
                    id="threshold-slider"
                    min={50}
                    max={90}
                    step={5}
                    value={threshold}
                    onChange={val => setThreshold(val)}
                    onMouseEnter={() => setShowThresholdTooltip(true)}
                    onMouseLeave={() => setShowThresholdTooltip(false)}
                  >
                    <SliderTrack>
                      <SliderFilledTrack />
                    </SliderTrack>
                    <SliderMark value={50} mt={2} ml={-2} fontSize="sm">
                      50%
                    </SliderMark>
                    <SliderMark value={70} mt={2} ml={-2} fontSize="sm">
                      70%
                    </SliderMark>
                    <SliderMark value={90} mt={2} ml={-2} fontSize="sm">
                      90%
                    </SliderMark>
                    <Tooltip
                      hasArrow
                      bg="blue.500"
                      color="white"
                      placement="top"
                      isOpen={showThresholdTooltip}
                      label={`${threshold}%: ${threshold <= 60 ? 'More results' : threshold >= 80 ? 'Better matches' : 'Balanced'}`}
                    >
                      <SliderThumb />
                    </Tooltip>
                  </Slider>
                </Box>

                <Button
                  colorScheme="blue"
                  onClick={handleShortlist}
                  isLoading={isShortlisting}
                  isDisabled={disabled}
                  leftIcon={<span>🔍</span>}
                  width="full"
                >
                  Shortlist Matches
                </Button>
              </VStack>
            </TabPanel>

            {/* RAG-based Shortlisting Panel */}
            <TabPanel p={4}>
              <VStack align="stretch" spacing={4}>
                <Text>
                  RAG-based shortlisting uses your uploaded documents to find the most relevant job
                  matches.
                </Text>

                <Button
                  colorScheme="blue"
                  onClick={openRagDialog}
                  isDisabled={!hasEmbedding || disabled}
                  leftIcon={<span>📊</span>}
                  width="full"
                >
                  Start RAG Shortlisting
                </Button>

                {!hasEmbedding && (
                  <Alert status="info">
                    <AlertIcon />
                    Upload and embed documents first to enable RAG-based shortlisting
                  </Alert>
                )}
              </VStack>
            </TabPanel>
          </TabPanels>
        </Tabs>

        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}

        <Button
          colorScheme="green"
          onClick={handleEvaluate}
          isLoading={isEvaluating}
          isDisabled={disabled || !shortlistResults}
          leftIcon={<span>✓</span>}
          width="full"
        >
          Evaluate Matches
        </Button>
      </VStack>

      {/* RAG Method Selection Dialog */}
      <Modal isOpen={isRagDialogOpen} onClose={closeRagDialog}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Select RAG Shortlisting Method</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack align="stretch" spacing={4}>
              <Text>Choose the method for RAG-based shortlisting:</Text>

              <RadioGroup onChange={setRagMethod} value={ragMethod}>
                <Stack direction="column" spacing={4}>
                  <Radio value="faiss_preselect">
                    <Box>
                      <Text fontWeight="bold">FAISS Pre-selection</Text>
                      <Text fontSize="sm">
                        Uses vector similarity to pre-filter documents before LLM analysis. Faster
                        but might miss subtle matches.
                      </Text>
                    </Box>
                  </Radio>

                  <Radio value="full_llm">
                    <Box>
                      <Text fontWeight="bold">Full LLM Selection</Text>
                      <Text fontSize="sm">
                        Uses LLM to analyze all documents for nuanced matching. More thorough but
                        slower.
                      </Text>
                    </Box>
                  </Radio>
                </Stack>
              </RadioGroup>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={closeRagDialog}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={handleRagShortlist} isLoading={isShortlisting}>
              Start Shortlisting
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default ShortlistControls;
