from fastapi import APIRouter, HTTPException, Depends, Body
from typing import Dict, Any
import os
import pandas as pd
from ..core.config import settings
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix=f"{settings.API_V1_STR}/cv",
    tags=["cv"],
    responses={404: {"description": "Not found"}},
)

# Path to job_sites.xlsx
JOB_SITES_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))), "job_sites.xlsx")

@router.get("/summary")
async def get_cv_summary():
    """
    Get CV summary from job_sites.xlsx
    """
    try:
        logger.info(f"Loading CV summary from: {JOB_SITES_PATH}")
        
        # Check if file exists
        if not os.path.exists(JOB_SITES_PATH):
            logger.error(f"File not found: {JOB_SITES_PATH}")
            raise HTTPException(status_code=404, detail="job_sites.xlsx not found")
        
        # Read the CV sheet from Excel
        df = pd.read_excel(JOB_SITES_PATH, sheet_name="CV", header=None)
        
        # Get the CV summary from cell B2 (index [1,1])
        cv_summary = str(df.iloc[1, 1]).strip()  # B2 is row 2, column B
        
        if pd.isna(cv_summary):
            logger.warning("CV summary cell (B2) is empty")
            return {"summary": "", "message": "CV summary is empty"}
            
        logger.info(f"Successfully loaded CV summary ({len(cv_summary)} characters)")
        return {"summary": cv_summary}
        
    except Exception as e:
        logger.error(f"Error loading CV summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading CV summary: {str(e)}")

@router.post("/summary")
async def update_cv_summary(data: Dict[str, Any] = Body(...)):
    """
    Update CV summary in job_sites.xlsx
    """
    try:
        summary = data.get("summary")
        if not summary:
            raise HTTPException(status_code=400, detail="CV summary is required")
        
        logger.info(f"Updating CV summary in: {JOB_SITES_PATH}")
        
        # Check if file exists
        if not os.path.exists(JOB_SITES_PATH):
            logger.error(f"File not found: {JOB_SITES_PATH}")
            raise HTTPException(status_code=404, detail="job_sites.xlsx not found")
        
        # Read the Excel file
        try:
            # Read the entire file
            xls = pd.ExcelFile(JOB_SITES_PATH)
            
            # Read all sheets
            sheets = {sheet_name: pd.read_excel(xls, sheet_name=sheet_name) 
                     for sheet_name in xls.sheet_names}
            
            # Modify the CV sheet
            cv_sheet = sheets["CV"]
            
            # Update cell B2 with the new summary
            cv_sheet.iloc[1, 1] = summary
            
            # Create a writer to save the modified Excel file
            with pd.ExcelWriter(JOB_SITES_PATH) as writer:
                for sheet_name, sheet_data in sheets.items():
                    sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
            
            logger.info(f"Successfully updated CV summary ({len(summary)} characters)")
            return {"message": "CV summary updated successfully"}
            
        except Exception as e:
            logger.error(f"Error updating Excel file: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating Excel file: {str(e)}")
        
    except Exception as e:
        logger.error(f"Error updating CV summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating CV summary: {str(e)}")
