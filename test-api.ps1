# Test API Script
# This script tests the backend API endpoints

Write-Host '===== Testing Backend API =====' -ForegroundColor Cyan
Write-Host ''

# Test job-url-md endpoint
try {
    Write-Host 'Testing job-url-md endpoint...' -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri 'http://localhost:8000/api/v1/job-url-md' -Method GET
    
    if ($response.StatusCode -eq 200) {
        Write-Host 'Success! Status code: 200' -ForegroundColor Green
        
        # Parse the JSON response
        $jobUrls = $response.Content | ConvertFrom-Json
        
        Write-Host "Found $($jobUrls.Count) job URLs:" -ForegroundColor Green
        
        # Display the job URLs
        foreach ($jobUrl in $jobUrls) {
            Write-Host "ID: $($jobUrl.id), URL: $($jobUrl.url), Prefix: $($jobUrl.prefix)" -ForegroundColor White
        }
    } else {
        Write-Host "Error! Status code: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "Error testing job-url-md endpoint: $_" -ForegroundColor Red
}

Write-Host ''
Write-Host 'API testing complete.' -ForegroundColor Cyan
