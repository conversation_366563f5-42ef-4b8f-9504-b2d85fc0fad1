/**
 * Simplified CV component for JoMaDe application.
 * This replaces the complex UserSettings component with a simpler approach.
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Textarea,
  FormControl,
  FormLabel,
  FormHelperText,
  useToast,
  VStack,
  Alert,
  AlertIcon,
  Text,
  Spinner,
  Flex,
} from '@chakra-ui/react';
import { FiSave } from 'react-icons/fi';
import { simpleApi } from '../services/simpleApi';

const CV: React.FC = () => {
  const [cvSummary, setCvSummary] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const toast = useToast();

  // Fetch CV summary on component mount
  useEffect(() => {
    const fetchCVSummary = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const data = await simpleApi.getCVSummary();
        setCvSummary(data.summary || '');
      } catch (err) {
        console.error('Error fetching CV summary:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch CV summary');
        
        toast({
          title: 'Error',
          description: err instanceof Error ? err.message : 'Failed to fetch CV summary',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCVSummary();
  }, [toast]);

  // Handle CV summary update
  const handleSaveCVSummary = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await simpleApi.updateCVSummary(cvSummary);

      toast({
        title: 'CV Summary saved',
        description: 'Your CV summary has been updated successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error saving CV summary:', err);
      setError(err instanceof Error ? err.message : 'Failed to save CV summary');
      
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to save CV summary',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box borderWidth="1px" borderRadius="lg" p={4}>
      <VStack spacing={4} align="stretch">
        <Text fontSize="lg" fontWeight="bold">CV Summary</Text>
        <Text fontSize="sm" color="gray.500">
          Edit your CV summary for job matching. This summary will be used to match jobs to your profile.
        </Text>
        
        {isLoading && (
          <Flex justify="center" p={4}>
            <Spinner size="md" />
          </Flex>
        )}
        
        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}
        
        <FormControl isDisabled={isLoading}>
          <FormLabel>CV Summary</FormLabel>
          <Textarea
            value={cvSummary}
            onChange={e => setCvSummary(e.target.value)}
            placeholder="Enter your CV summary here..."
            minHeight="200px"
            resize="vertical"
            isDisabled={isLoading}
          />
          <FormHelperText>
            This summary will be used for matching jobs to your profile
          </FormHelperText>
        </FormControl>

        <Button
          leftIcon={<FiSave />}
          colorScheme="blue"
          onClick={handleSaveCVSummary}
          alignSelf="flex-end"
          isLoading={isLoading}
          loadingText="Saving..."
        >
          Save Summary
        </Button>
      </VStack>
    </Box>
  );
};

export default CV;
