from typing import List, Optional, Dict, Any
from pydantic_settings import BaseSettings
from functools import lru_cache
from pydantic import field_validator, Field, ValidationInfo
import os
import logging

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    All application configuration should be defined here.
    """

    class Config:
        """Configuration for the Settings class."""
        # Load environment variables from .env file in the root directory
        env_file = ".env"
        case_sensitive = True
        # Ignore extra fields to allow frontend variables in .env
        extra = "ignore"

    # API Settings
    PROJECT_NAME: str = "JoMaDe"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Server Settings
    BACKEND_HOST: str = "0.0.0.0"
    BACKEND_PORT: int = 8000
    DEBUG: bool = False
    SQL_DEBUG: bool = False

    # Database Settings
    DATABASE_URL: str = "sqlite:///jobscraper.db"
    REDIS_URL: Optional[str] = None

    # API Keys
    OPENAI_API_KEY: Optional[str] = "dummy-key"
    KADOA_API_KEY: Optional[str] = "dummy-key"
    OCTOPARSE_API_KEY: Optional[str] = None
    FIRECRAWL_API_KEY: Optional[str] = None
    GOOGLE_API_KEY: Optional[str] = None
    GOOGLE_CX: Optional[str] = None

    # API URLs
    KADOA_API_URL: str = "https://api.kadoa.com/v4"

    # CORS Settings
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000"]
    ALLOWED_METHODS: List[str] = ["GET", "POST", "PUT", "DELETE"]
    ALLOWED_HEADERS: List[str] = ["*"]

    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60

    # Security
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Storage
    FAISS_INDEX_PATH: str = "./data/faiss_index"
    DATA_DIR: str = Field(default="./data")
    EMBEDDINGS_DIR: str = Field(default="./data/embeddings")
    DOCUMENTS_DIR: str = Field(default="./data/documents")
    JOBS_DIR: str = Field(default="./data/jobs")

    # S3 Storage (optional)
    S3_ENDPOINT: Optional[str] = None
    S3_ACCESS_KEY: Optional[str] = None
    S3_SECRET_KEY: Optional[str] = None
    S3_BUCKET: Optional[str] = None

    # File Paths
    JOB_SITES_EXCEL_PATH: str = Field(default="./job_sites.xlsx")

    # Validators for required API keys
    @field_validator("KADOA_API_KEY")
    def validate_kadoa_api_key(cls, v):
        if not v:
            logger.warning("KADOA_API_KEY is not set. Using dummy key.")
            return "dummy-key"
        return v

    @field_validator("OPENAI_API_KEY")
    def validate_openai_api_key(cls, v):
        if not v:
            logger.warning("OPENAI_API_KEY is not set. Using dummy key.")
            return "dummy-key"
        return v

    # Validator for database URL
    @field_validator("DATABASE_URL")
    def validate_database_url(cls, v, info: ValidationInfo):
        # If we're in development mode and using SQLite, make sure the path is absolute
        if v.startswith("sqlite:///") and not v.startswith("sqlite:////"):
            # Convert relative path to absolute path
            db_path = v.replace("sqlite:///", "")
            if not os.path.isabs(db_path):
                # Get the absolute path to the backend directory
                backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                abs_db_path = os.path.join(backend_dir, db_path)
                return f"sqlite:///{abs_db_path}"
        return v

    # Validators for directory paths
    @field_validator("DATA_DIR", "EMBEDDINGS_DIR", "DOCUMENTS_DIR", "JOBS_DIR", "FAISS_INDEX_PATH")
    def validate_directory_paths(cls, v, info: ValidationInfo):
        """Ensure directory paths are absolute"""
        if not os.path.isabs(v):
            # Get the absolute path to the project root
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            abs_path = os.path.join(root_dir, v.lstrip("./"))
            field_name = info.field_name
            logger.debug(f"Converting relative path {v} to absolute path {abs_path} for {field_name}")
            return abs_path
        return v

    @field_validator("JOB_SITES_EXCEL_PATH")
    def validate_excel_path(cls, v, info: ValidationInfo):
        """Ensure the job sites Excel file path is absolute"""
        if not os.path.isabs(v):
            # Get the absolute path to the project root
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            abs_path = os.path.join(root_dir, v.lstrip("./"))
            logger.debug(f"Converting relative path {v} to absolute path {abs_path} for JOB_SITES_EXCEL_PATH")
            return abs_path
        return v




@lru_cache()
def get_settings() -> Settings:
    """
    Create and return a Settings instance.
    Uses LRU cache to avoid loading the settings multiple times.
    """
    try:
        logger.info("Loading application settings from environment variables")
        settings = Settings()

        # Log important settings
        logger.info(f"Application: {settings.PROJECT_NAME} v{settings.VERSION}")
        logger.info(f"Debug mode: {settings.DEBUG}")
        logger.info(f"Database URL: {settings.DATABASE_URL}")
        logger.info(f"Data directory: {settings.DATA_DIR}")

        # Log API key availability (without revealing the keys)
        api_keys = {
            "OPENAI_API_KEY": bool(settings.OPENAI_API_KEY),
            "KADOA_API_KEY": bool(settings.KADOA_API_KEY),
            "FIRECRAWL_API_KEY": bool(settings.FIRECRAWL_API_KEY),
            "OCTOPARSE_API_KEY": bool(settings.OCTOPARSE_API_KEY),
            "GOOGLE_API_KEY": bool(settings.GOOGLE_API_KEY)
        }
        logger.info(f"API keys available: {api_keys}")

        # Warn about debug mode in production
        if settings.DEBUG:
            import warnings
            warnings.warn("Running in DEBUG mode is not recommended in production!")

        return settings
    except Exception as e:
        logger.error(f"Error loading settings: {str(e)}")
        raise

# Create a singleton instance
settings = get_settings()