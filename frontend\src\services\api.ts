import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import {
  Job,
  CVSummary,
  Document,
  EmbeddingInfo,
  Workflow,
  EvaluationResult,
  JobSource,
  ApiResponse,
  PaginatedResponse,
  SystemSettings,
  UserSettings,
} from '../types';

// Create a base axios instance with common configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds
});

// Request interceptor
apiClient.interceptors.request.use(
  config => {
    // You can add auth token here if needed
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  response => {
    return response;
  },
  (error: AxiosError) => {
    // Handle common error cases
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('API Error Response:', error.response.data);

      // Handle specific status codes
      switch (error.response.status) {
        case 401:
          // Unauthorized - handle authentication error
          console.error('Authentication error');
          break;
        case 403:
          // Forbidden - handle authorization error
          console.error('Authorization error');
          break;
        case 404:
          // Not found
          console.error('Resource not found');
          break;
        case 500:
          // Server error
          console.error('Server error');
          break;
        default:
          // Other errors
          console.error(`Error with status code: ${error.response.status}`);
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error setting up request:', error.message);
    }

    return Promise.reject(error);
  }
);

// Generic API methods
export const api = {
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get<T>(url, config);
    return response.data;
  },

  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post<T>(url, data, config);
    return response.data;
  },

  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put<T>(url, data, config);
    return response.data;
  },

  patch: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.patch<T>(url, data, config);
    return response.data;
  },

  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete<T>(url, config);
    return response.data;
  },

  // Method for file uploads
  upload: async <T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post<T>(url, formData, {
      ...config,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

// API service class
export class ApiService {
  // Jobs endpoints
  async getJobs(): Promise<Job[]> {
    return api.get<Job[]>('/api/v1/jobs');
  }

  async shortlistJobs(
    temperature: number = 0.7,
    threshold: number = 70
  ): Promise<{
    shortlisted_count: number;
    shortlisted_jobs: Job[];
  }> {
    return api.post('/api/v1/jobs/shortlist', {
      temperature,
      threshold,
    });
  }

  async evaluateJobs(
    jobIds: string[] = [],
    temperature: number = 0.7
  ): Promise<{
    evaluated_count: number;
    evaluated_jobs: EvaluationResult[];
  }> {
    return api.post('/api/v1/jobs/evaluate', {
      job_ids: jobIds,
      temperature,
    });
  }

  // CV endpoints
  async getCVSummary(): Promise<CVSummary> {
    return api.get<CVSummary>('/api/v1/cv/summary');
  }

  async updateCVSummary(summary: string): Promise<CVSummary> {
    return api.post<CVSummary>('/api/v1/cv/summary', { summary });
  }

  // Documents endpoints
  async uploadDocument(file: File): Promise<Document> {
    const formData = new FormData();
    formData.append('file', file);
    return api.upload<Document>('/api/v1/documents/upload', formData);
  }

  async getDocuments(): Promise<Document[]> {
    return api.get<Document[]>('/api/v1/documents');
  }

  // Job Sources endpoints
  async getJobSources(): Promise<JobSource[]> {
    return api.get<JobSource[]>('/api/v1/job_sources');
  }

  async createJobSource(url: string, name?: string): Promise<JobSource> {
    return api.post<JobSource>('/api/v1/job_sources', { url, name });
  }

  async updateJobSource(id: string, data: Partial<JobSource>): Promise<JobSource> {
    return api.put<JobSource>(`/api/v1/job_sources/${id}`, data);
  }

  async deleteJobSource(id: string): Promise<void> {
    return api.delete<void>(`/api/v1/job_sources/${id}`);
  }

  // Settings endpoints
  async getSystemSettings(): Promise<SystemSettings> {
    return api.get<SystemSettings>('/api/v1/settings/system');
  }

  async updateSystemSettings(settings: Partial<SystemSettings>): Promise<SystemSettings> {
    return api.put<SystemSettings>('/api/v1/settings/system', settings);
  }

  async getUserSettings(): Promise<UserSettings> {
    return api.get<UserSettings>('/api/v1/settings/user');
  }

  async updateUserSettings(settings: Partial<UserSettings>): Promise<UserSettings> {
    return api.put<UserSettings>('/api/v1/settings/user', settings);
  }

  // Health check
  async checkHealth(): Promise<{ status: string }> {
    try {
      return api.get<{ status: string }>('/health');
    } catch (error) {
      return { status: 'error' };
    }
  }
}

// Create a singleton instance
export const apiService = new ApiService();

export default apiClient;
