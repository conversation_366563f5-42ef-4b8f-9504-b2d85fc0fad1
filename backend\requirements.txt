# Core FastAPI and web framework dependencies
fastapi==0.109.2
uvicorn[standard]==0.27.1
starlette==0.36.3

# Database and ORM
sqlalchemy==2.0.27
psycopg2-binary==2.9.9
alembic==1.13.1

# Configuration and environment
python-dotenv==1.0.1
pydantic==2.6.1
pydantic-settings==2.1.0

# Security and authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.9

# Data processing and storage
redis==5.0.1
faiss-cpu==1.7.4
numpy==1.26.4
pandas==2.2.1

# Web scraping and content processing
beautifulsoup4==4.12.3
requests==2.31.0
aiohttp==3.9.3
bleach==6.1.0

# AI and LLM dependencies
langchain>=0.0.200
openai>=0.27.0
tiktoken>=0.4.0

# Document processing
pypdf>=3.8.1
docx2txt>=0.8
unstructured>=0.6.6

# Testing and development
pytest==8.0.1
httpx==0.26.0

# System utilities
psutil==5.9.8

# Additional dependencies for proper functionality
click>=8.0.0
typing-extensions>=4.8.0
annotated-types>=0.4.0
anyio>=3.0.0
sniffio>=1.1.0
idna>=2.8
certifi>=2021.5.25
charset-normalizer>=2.0.0
urllib3>=1.26.0
