from fastapi import APIRouter, HTTPException, Depends, File, UploadFile, Form
from typing import Dict, Any, List
import os
import shutil
from ..core.config import settings
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix=f"{settings.API_V1_STR}/documents",
    tags=["documents"],
    responses={404: {"description": "Not found"}},
)

# Path to documents directory
DOCUMENTS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))), "documents")

# Create documents directory if it doesn't exist
os.makedirs(DOCUMENTS_DIR, exist_ok=True)

@router.post("/upload")
async def upload_documents(files: List[UploadFile] = File(...)):
    """
    Upload documents (CV, etc.)
    """
    try:
        logger.info(f"Uploading {len(files)} documents")
        
        # Create documents directory if it doesn't exist
        os.makedirs(DOCUMENTS_DIR, exist_ok=True)
        
        uploaded_files = []
        
        for file in files:
            # Create file path
            file_path = os.path.join(DOCUMENTS_DIR, file.filename)
            
            # Save file
            with open(file_path, "wb") as f:
                shutil.copyfileobj(file.file, f)
            
            uploaded_files.append({
                "filename": file.filename,
                "content_type": file.content_type,
                "size": os.path.getsize(file_path)
            })
            
            logger.info(f"Uploaded file: {file.filename}")
        
        return {
            "message": f"Successfully uploaded {len(uploaded_files)} files",
            "files": uploaded_files
        }
        
    except Exception as e:
        logger.error(f"Error uploading documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error uploading documents: {str(e)}")

@router.get("/")
async def get_documents():
    """
    Get list of uploaded documents
    """
    try:
        logger.info("Getting list of documents")
        
        # Create documents directory if it doesn't exist
        os.makedirs(DOCUMENTS_DIR, exist_ok=True)
        
        # Get list of files
        files = []
        for filename in os.listdir(DOCUMENTS_DIR):
            file_path = os.path.join(DOCUMENTS_DIR, filename)
            if os.path.isfile(file_path):
                files.append({
                    "filename": filename,
                    "size": os.path.getsize(file_path),
                    "last_modified": os.path.getmtime(file_path)
                })
        
        logger.info(f"Found {len(files)} documents")
        return files
        
    except Exception as e:
        logger.error(f"Error getting documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting documents: {str(e)}")

@router.delete("/{filename}")
async def delete_document(filename: str):
    """
    Delete a document
    """
    try:
        logger.info(f"Deleting document: {filename}")
        
        # Create file path
        file_path = os.path.join(DOCUMENTS_DIR, filename)
        
        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"File not found: {filename}")
            raise HTTPException(status_code=404, detail=f"File not found: {filename}")
        
        # Delete file
        os.remove(file_path)
        
        logger.info(f"Successfully deleted document: {filename}")
        return {"message": f"Successfully deleted document: {filename}"}
        
    except Exception as e:
        logger.error(f"Error deleting document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")
