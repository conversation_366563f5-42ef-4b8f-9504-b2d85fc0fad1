/**
 * Simplified JobUrls component for JoMaDe application.
 * This replaces the complex URLManagement component with a simpler approach.
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Input,
  Checkbox,
  IconButton,
  HStack,
  VStack,
  FormControl,
  FormLabel,
  useToast,
  Badge,
  Text,
  Flex,
  useColorModeValue,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Alert,
  AlertIcon,
  Spinner,
} from '@chakra-ui/react';
import { FiPlus, FiEdit2, FiTrash2, FiRefreshCw } from 'react-icons/fi';
import { JobSource } from '../types';
import { simpleApi } from '../services/simpleApi';

const JobUrls: React.FC = () => {
  const [jobSources, setJobSources] = useState<JobSource[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [newUrl, setNewUrl] = useState<string>('');
  const [newName, setNewName] = useState<string>('');
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [editingSource, setEditingSource] = useState<JobSource | null>(null);

  const toast = useToast();
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Function to fetch job URLs from the backend
  const fetchJobUrls = async (showToast = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const sources = await simpleApi.getJobUrls();
      setJobSources(sources);

      if (showToast && sources.length > 0) {
        toast({
          title: 'Job URLs Loaded',
          description: `Successfully loaded ${sources.length} job URLs`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err) {
      console.error('Error fetching job URLs:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch job URLs');
      
      toast({
        title: 'Error Loading Job URLs',
        description: err instanceof Error ? err.message : 'Failed to fetch job URLs',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle refreshing job URLs
  const handleRefresh = () => {
    fetchJobUrls(true);
  };

  // Load job sources from the backend on component mount
  useEffect(() => {
    fetchJobUrls();
  }, []);

  // Handle adding a new URL
  const handleAddUrl = async () => {
    if (!newUrl) {
      toast({
        title: 'URL is required',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsLoading(true);

    try {
      await simpleApi.addJobUrl(newUrl, newName || undefined);
      
      // Refresh the job URLs list
      await fetchJobUrls();
      
      setNewUrl('');
      setNewName('');
      onClose();

      toast({
        title: 'URL added',
        description: 'The URL has been added successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error adding URL:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to add URL';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle editing a URL
  const handleEditUrl = (source: JobSource) => {
    setEditingSource(source);
    setNewUrl(source.url);
    setNewName(source.name || '');
    onOpen();
  };

  // Handle saving edited URL
  const handleSaveEdit = async () => {
    if (!editingSource) return;

    setIsLoading(true);

    try {
      await simpleApi.updateJobUrl(editingSource.id, {
        url: newUrl,
        name: newName || newUrl,
      });
      
      // Refresh the job URLs list
      await fetchJobUrls();
      
      setEditingSource(null);
      setNewUrl('');
      setNewName('');
      onClose();

      toast({
        title: 'URL updated',
        description: 'The URL has been updated successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error updating URL:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update URL';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle deleting URLs
  const handleDeleteUrls = async () => {
    if (selectedSources.length === 0) return;

    setIsLoading(true);
    setError(null);

    try {
      // Delete each selected URL
      for (const id of selectedSources) {
        await simpleApi.deleteJobUrl(id);
      }
      
      // Refresh the job URLs list
      await fetchJobUrls();
      
      setSelectedSources([]);

      toast({
        title: 'URLs deleted',
        description: `${selectedSources.length} URL(s) have been deleted`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error deleting URLs:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete URLs';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle selecting all sources
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedSources(jobSources.map(source => source.id));
    } else {
      setSelectedSources([]);
    }
  };

  // Handle selecting a single source
  const handleSelectSource = (id: string, isChecked: boolean) => {
    if (isChecked) {
      setSelectedSources([...selectedSources, id]);
    } else {
      setSelectedSources(selectedSources.filter(sourceId => sourceId !== id));
    }
  };

  return (
    <Box>
      <VStack spacing={4} align="stretch">
        <VStack spacing={2} align="stretch">
          {/* URL Management Controls */}
          <Flex justify="space-between" align="center" wrap="wrap" gap={1}>
            <Button leftIcon={<FiPlus />} colorScheme="blue" size="sm" onClick={onOpen}>
              Add URL
            </Button>

            <HStack spacing={1}>
              <Button
                leftIcon={<FiTrash2 />}
                colorScheme="red"
                variant="outline"
                size="sm"
                isDisabled={selectedSources.length === 0}
                onClick={handleDeleteUrls}
              >
                Delete Selected
              </Button>
              <Button
                leftIcon={<FiRefreshCw />}
                colorScheme="green"
                variant="outline"
                size="sm"
                isLoading={isLoading}
                onClick={handleRefresh}
              >
                Refresh
              </Button>
            </HStack>
          </Flex>

          {error && (
            <Alert status="error" mt={1} py={1} fontSize="sm">
              <AlertIcon boxSize="12px" />
              {error}
            </Alert>
          )}

        {/* URL Table */}
        <Box borderWidth="1px" borderRadius="lg" borderColor={borderColor} overflow="hidden">
          {isLoading ? (
            <Flex justify="center" align="center" p={4}>
              <Spinner size="md" color="blue.500" />
            </Flex>
          ) : (
            <Table variant="simple" size="sm">
              <Thead>
                <Tr>
                  <Th width="20px" px={1} py={1}>
                    <Checkbox
                      size="sm"
                      isChecked={
                        selectedSources.length === jobSources.length && jobSources.length > 0
                      }
                      isIndeterminate={
                        selectedSources.length > 0 && selectedSources.length < jobSources.length
                      }
                      onChange={handleSelectAll}
                    />
                  </Th>
                  <Th width="60px" px={1} py={1}>Prefix</Th>
                  <Th width="80px" px={1} py={1}>Name</Th>
                  <Th px={1} py={1} width="350px">URL</Th>
                  <Th width="60px" px={1} py={1}>Actions</Th>
                </Tr>
              </Thead>
            <Tbody>
              {jobSources.length === 0 ? (
                <Tr>
                  <Td colSpan={5} textAlign="center" py={1}>
                    <Text fontSize="sm">No URLs found. Add a URL to get started.</Text>
                  </Td>
                </Tr>
              ) : (
                jobSources.map((source) => (
                  <Tr key={source.id}>
                    <Td px={1} py={1}>
                      <Checkbox
                        size="sm"
                        isChecked={selectedSources.includes(source.id)}
                        onChange={(e) => handleSelectSource(source.id, e.target.checked)}
                      />
                    </Td>
                    <Td px={1} py={1} fontSize="xs">
                      <Badge colorScheme="blue">{source.prefix}</Badge>
                    </Td>
                    <Td px={1} py={1} fontSize="xs">
                      {source.name || `Source ${source.id}`}
                    </Td>
                    <Td px={1} py={1} fontSize="xs">
                      <Text noOfLines={1} title={source.url}>
                        {source.url}
                      </Text>
                    </Td>
                    <Td px={1} py={1}>
                      <HStack spacing={1}>
                        <IconButton
                          aria-label="Edit URL"
                          icon={<FiEdit2 />}
                          size="xs"
                          variant="ghost"
                          onClick={() => handleEditUrl(source)}
                        />
                        <IconButton
                          aria-label="Delete URL"
                          icon={<FiTrash2 />}
                          size="xs"
                          variant="ghost"
                          colorScheme="red"
                          onClick={() => {
                            setSelectedSources([source.id]);
                            handleDeleteUrls();
                          }}
                        />
                      </HStack>
                    </Td>
                  </Tr>
                ))
              )}
            </Tbody>
            </Table>
          )}
        </Box>
      </VStack>

      {/* Add/Edit URL Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="sm">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader fontSize="md" py={2}>{editingSource ? 'Edit URL' : 'Add URL'}</ModalHeader>
          <ModalCloseButton size="sm" />
          <ModalBody pb={3} pt={0}>
            <VStack spacing={2}>
              <FormControl isRequired size="sm">
                <FormLabel fontSize="sm">URL</FormLabel>
                <Input
                  size="sm"
                  placeholder="https://example.com/jobs"
                  value={newUrl}
                  onChange={e => setNewUrl(e.target.value)}
                />
              </FormControl>
              <FormControl size="sm">
                <FormLabel fontSize="sm">Name (Optional)</FormLabel>
                <Input
                  size="sm"
                  placeholder="Example Jobs"
                  value={newName}
                  onChange={e => setNewName(e.target.value)}
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter py={2}>
            <Button variant="ghost" mr={2} size="sm" onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="blue" size="sm" onClick={editingSource ? handleSaveEdit : handleAddUrl}>
              {editingSource ? 'Save' : 'Add'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default JobUrls;
