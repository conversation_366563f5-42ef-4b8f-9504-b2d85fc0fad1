'use client';

import React, { ReactNode } from 'react';
import {
  Box,
  Flex,
  Heading,
  Text,
  useColorModeValue,
  IconButton,
  Collapse,
  useDisclosure,
  Spinner,
  Divider,
} from '@chakra-ui/react';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';

interface CardProps {
  title?: string;
  subtitle?: string;
  children: ReactNode;
  footer?: ReactNode;
  isLoading?: boolean;
  isCollapsible?: boolean;
  initiallyCollapsed?: boolean;
  headerAction?: ReactNode;
  variant?: 'default' | 'outline' | 'elevated';
  minHeight?: string | number;
}

const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  children,
  footer,
  isLoading = false,
  isCollapsible = false,
  initiallyCollapsed = false,
  headerAction,
  variant = 'default',
  minHeight,
}) => {
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: !initiallyCollapsed });

  // Color scheme
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const headerBgColor = useColorModeValue('gray.50', 'gray.700');
  const footerBgColor = useColorModeValue('gray.50', 'gray.700');

  // Variant styles
  const variantStyles = {
    default: {
      border: '1px solid',
      borderColor: borderColor,
      shadow: 'sm',
    },
    outline: {
      border: '1px solid',
      borderColor: borderColor,
      shadow: 'none',
    },
    elevated: {
      border: 'none',
      shadow: 'md',
    },
  };

  return (
    <Box
      borderRadius="lg"
      overflow="hidden"
      bg={bgColor}
      minHeight={minHeight}
      {...variantStyles[variant]}
    >
      {/* Card Header */}
      {(title || isCollapsible) && (
        <Flex
          p={4}
          align="center"
          justify="space-between"
          bg={variant !== 'default' ? 'transparent' : headerBgColor}
          borderBottom={variant !== 'default' ? 'none' : '1px solid'}
          borderBottomColor={borderColor}
        >
          <Box>
            {title && (
              <Heading size="md" fontWeight="semibold">
                {title}
              </Heading>
            )}
            {subtitle && (
              <Text fontSize="sm" color="gray.500" mt={1}>
                {subtitle}
              </Text>
            )}
          </Box>

          <Flex align="center">
            {headerAction && <Box mr={isCollapsible ? 2 : 0}>{headerAction}</Box>}

            {isCollapsible && (
              <IconButton
                aria-label={isOpen ? 'Collapse' : 'Expand'}
                icon={isOpen ? <FiChevronUp /> : <FiChevronDown />}
                size="sm"
                variant="ghost"
                onClick={onToggle}
              />
            )}
          </Flex>
        </Flex>
      )}

      {/* Card Body */}
      <Collapse in={!isCollapsible || isOpen} animateOpacity>
        <Box position="relative" p={4}>
          {isLoading ? (
            <Flex justify="center" align="center" minH="100px">
              <Spinner size="lg" color="blue.500" />
            </Flex>
          ) : (
            children
          )}
        </Box>
      </Collapse>

      {/* Card Footer */}
      {footer && (
        <>
          <Divider />
          <Box
            p={4}
            bg={variant !== 'default' ? 'transparent' : footerBgColor}
            borderTop={variant !== 'default' ? 'none' : '1px solid'}
            borderTopColor={borderColor}
          >
            {footer}
          </Box>
        </>
      )}
    </Box>
  );
};

export default Card;
