{"name": "joma<PERSON>-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "npx next dev", "build": "npx next build", "start": "npx next start", "lint": "npx next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@tanstack/react-query": "^5.20.5", "axios": "^1.6.7", "framer-motion": "^11.0.3", "next": "14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "zustand": "^4.5.0"}, "devDependencies": {"@types/node": "^20.11.16", "@types/react": "^18.2.52", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.2.5", "typescript": "^5.3.3"}}