{"name": "j<PERSON><PERSON>", "version": "0.1.0", "private": true, "workspaces": ["frontend"], "scripts": {"dev": "npm run dev --workspace=frontend", "build": "npm run build --workspace=frontend", "start": "npm run start --workspace=frontend", "lint": "npm run lint --workspace=frontend", "type-check": "npm run type-check --workspace=frontend"}, "dependencies": {"@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@tanstack/react-query": "^5.20.5", "axios": "^1.6.7", "framer-motion": "^11.0.3", "next": "14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "zustand": "^4.5.0"}, "devDependencies": {"@types/node": "^20.11.16", "@types/react": "^18.2.52", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.2.5", "typescript": "^5.3.3", "jest": "^29.7.0", "@testing-library/react": "^14.2.1", "@testing-library/jest-dom": "^6.4.2"}}