'use client';

import React from 'react';
import {
  Box,
  Text,
  VStack,
  <PERSON>ing,
  Flex,
  List,
  ListItem,
  Badge,
  Divider,
  Alert,
  AlertIcon,
  Spinner,
  Button,
} from '@chakra-ui/react';
import { useEvaluationStore } from '../stores/evaluationStore';

interface EvaluationResultsProps {
  onEvaluate?: () => void;
}

const EvaluationResults: React.FC<EvaluationResultsProps> = ({ onEvaluate }) => {
  // Use the evaluation store
  const { results, isLoading, error, counts } = useEvaluationStore();

  // Get color scheme for badges
  const getColorScheme = (color: string) => {
    switch (color) {
      case 'green':
        return 'green';
      case 'orange':
        return 'orange';
      case 'red':
        return 'red';
      default:
        return 'gray';
    }
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="white">
      <VStack align="stretch" spacing={4}>
        <Flex justify="space-between" align="center">
          <Heading size="md">Evaluation Results</Heading>
          <Text>
            Total: {counts.total},
            <Text as="span" color="green.500" mx={1}>
              Green: {counts.green}
            </Text>
            ,
            <Text as="span" color="orange.500" mx={1}>
              Orange: {counts.orange}
            </Text>
            ,
            <Text as="span" color="red.500" mx={1}>
              Red: {counts.red}
            </Text>
          </Text>
        </Flex>

        {isLoading ? (
          <Flex justify="center" align="center" py={8}>
            <Spinner size="xl" />
            <Text ml={4}>Evaluating jobs...</Text>
          </Flex>
        ) : error ? (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        ) : results.length > 0 ? (
          <List spacing={4}>
            {results.map(result => (
              <ListItem
                key={result.id}
                p={3}
                borderWidth="1px"
                borderRadius="md"
                borderLeftWidth="4px"
                borderLeftColor={`${result.color}.500`}
              >
                <VStack align="stretch" spacing={2}>
                  <Flex justify="space-between" align="center">
                    <Heading size="sm">{result.title}</Heading>
                    <Badge colorScheme={getColorScheme(result.color)}>Match: {result.score}%</Badge>
                  </Flex>

                  {result.shortlistScore && (
                    <Text fontSize="sm">Shortlist Score: {result.shortlistScore}%</Text>
                  )}

                  <Divider />

                  <Box>
                    <Text fontWeight="bold">Pros:</Text>
                    <Text>{result.pros}</Text>
                  </Box>

                  <Box>
                    <Text fontWeight="bold">Cons:</Text>
                    <Text>{result.cons}</Text>
                  </Box>
                </VStack>
              </ListItem>
            ))}
          </List>
        ) : (
          <VStack spacing={4} align="stretch">
            <Text color="gray.500">
              No evaluation results yet. Complete the shortlisting step first, then click "Evaluate
              Matches".
            </Text>

            {onEvaluate && (
              <Button colorScheme="green" onClick={onEvaluate} isDisabled={isLoading}>
                Start Evaluation
              </Button>
            )}
          </VStack>
        )}
      </VStack>
    </Box>
  );
};

export default EvaluationResults;
