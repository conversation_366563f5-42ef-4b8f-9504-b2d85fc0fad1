'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Text,
  Textarea,
  VStack,
  Heading,
  Flex,
  useToast,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';

interface CVSummaryProps {
  onCVSummaryChange?: (summary: string) => void;
}

const CVSummary: React.FC<CVSummaryProps> = ({ onCVSummaryChange }) => {
  const [cvSummary, setCVSummary] = useState<string>('');
  const [wordCount, setWordCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();

  // Calculate word count whenever CV summary changes
  useEffect(() => {
    const words = cvSummary.trim() ? cvSummary.trim().split(/\s+/).length : 0;
    setWordCount(words);

    // Notify parent component if callback provided
    if (onCVSummaryChange) {
      onCVSummaryChange(cvSummary);
    }
  }, [cvSummary, onCVSummaryChange]);

  // Handle CV summary change
  const handleCVSummaryChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCVSummary(e.target.value);
  };

  // Import CV summary from backend
  const importCVSummary = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:8000/api/v1/cv/summary');

      if (!response.ok) {
        throw new Error(`Failed to import CV summary: ${response.status}`);
      }

      const data = await response.json();
      setCVSummary(data.summary || '');

      toast({
        title: 'CV Summary Imported',
        description: 'Successfully imported CV summary',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error importing CV summary:', err);
      setError(err instanceof Error ? err.message : 'Failed to import CV summary');

      toast({
        title: 'Import Failed',
        description: err instanceof Error ? err.message : 'Failed to import CV summary',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="white">
      <VStack align="stretch" spacing={4}>
        <Flex justify="space-between" align="center">
          <Heading size="md">CV Summary</Heading>
          <Text fontSize="sm">Word Count: {wordCount}</Text>
        </Flex>

        <Button
          colorScheme="blue"
          onClick={importCVSummary}
          isLoading={isLoading}
          leftIcon={<span>📄</span>}
        >
          Import CV Summary
        </Button>

        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}

        <Textarea
          value={cvSummary}
          onChange={handleCVSummaryChange}
          placeholder="Enter or import your CV summary here..."
          size="md"
          minHeight="200px"
          resize="vertical"
        />
      </VStack>
    </Box>
  );
};

export default CVSummary;
