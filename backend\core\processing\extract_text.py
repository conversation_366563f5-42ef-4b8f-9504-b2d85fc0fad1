import cv2   
import numpy as np
import pytesseract
try:
    import fitz  # PyMuPDF
except ImportError as e:
    print("❌ Error: Could not import PyMuPDF (fitz)")
    print("Please install it using: pip install --upgrade PyMuPDF")
    print(f"Original error: {str(e)}")
    raise
import re
from pathlib import Path
import os
from typing import List, Optional, Callable

# Set Tesseract path
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def verify_tesseract() -> bool:
    """Verify Tesseract is available and working."""
    try:
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        
        # Try to get available languages
        try:
            langs = pytesseract.get_languages()
            if 'eng' not in langs or 'deu' not in langs:
                print("⚠️ Warning: English or German language data not found")
                print("Please install language data for eng and deu")
        except:
            print("⚠️ Warning: Could not verify language data")
        
        return True
    except Exception as e:
        print("\n❌ Tesseract Installation Error:")
        print("1. Install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Add Tesseract to your system PATH")
        print("3. Install language data for English (eng) and German (deu)")
        print("4. Restart the application")
        print(f"\nError details: {str(e)}")
        return False

def preprocess_image(image):
    """Enhance image for better OCR results."""
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Remove noise
    denoised = cv2.fastNlMeansDenoising(gray)
    
    # Increase contrast
    contrast = cv2.convertScaleAbs(denoised, alpha=1.5, beta=0)
    
    # Adaptive thresholding
    threshold = cv2.adaptiveThreshold(
        contrast,
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY,
        11,  # Block size
        2    # C constant
    )
    
    # Remove small noise
    kernel = np.ones((1, 1), np.uint8)
    cleaned = cv2.morphologyEx(threshold, cv2.MORPH_CLOSE, kernel)
    
    return cleaned

def determine_skew(image):
    """Determine skew angle of the text."""
    coords = np.column_stack(np.where(image > 0))
    angle = cv2.minAreaRect(coords.astype(np.float32))[-1]
    if angle < -45:
        angle = 90 + angle
    return -angle

def rotate_image(image, angle):
    """Rotate the image by given angle."""
    (h, w) = image.shape[:2]
    center = (w // 2, h // 2)
    M = cv2.getRotationMatrix2D(center, angle, 1.0)
    rotated = cv2.warpAffine(
        image, M, (w, h),
        flags=cv2.INTER_CUBIC,
        borderMode=cv2.BORDER_REPLICATE
    )
    return rotated

def clean_ocr_text(text: str) -> str:
    """Clean OCR text optimized for embeddings."""
    
    # Split into lines
    lines = text.split('\n')
    content_lines = []
    
    # Key information patterns
    important_patterns = [
        r'ausbildung|education',
        r'erfahrung|experience',
        r'qualifikation|qualification',
        r'verantwortung|responsibility',
        r'projekt|project',
        r'leistung|performance',
        r'führung|leadership',
        r'management',
        r'entwicklung|development',
        r'kompetenz|competence',
        r'erfolg|success'
    ]
    
    # Patterns to exclude
    exclude_patterns = [
        r'briefkopf|letterhead',
        r'seite|page',
        r'datum|date',
        r'unterschrift|signature',
        r'tel|fax|email',
        r'mit freundlichen grüßen|best regards'
    ]
    
    important_regex = '|'.join(f'(?:{pattern})' for pattern in important_patterns)
    exclude_regex = '|'.join(f'(?:{pattern})' for pattern in exclude_patterns)
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Skip headers, footers, and irrelevant content
        if re.search(exclude_regex, line.lower()):
            continue
            
        # Clean the line
        cleaned_line = re.sub(r'[^\w\s\.\-\,\:]', ' ', line)
        cleaned_line = re.sub(r'\s+', ' ', cleaned_line).strip()
        
        # Keep lines that:
        # 1. Contain important keywords
        # 2. Are part of content sections (longer lines)
        # 3. Contain specific date patterns (for experience timeline)
        if (re.search(important_regex, cleaned_line.lower()) or
            len(cleaned_line.split()) > 5 or
            re.search(r'\d{4}.*\d{4}|\d{2}\.\d{2}\.\d{4}', cleaned_line)):
            content_lines.append(cleaned_line)
    
    # Join lines with proper spacing
    text = ' '.join(content_lines)
    
    # Final cleanup
    text = re.sub(r'\s+', ' ', text)  # Remove multiple spaces
    text = re.sub(r'(?<=[a-z])\.(?=[A-Z])', '. ', text)  # Fix sentence spacing
    
    return text.strip()

def extract_text_from_scanned_pdf(pdf_path: str, progress_callback: Callable = None) -> str:
    """Extract text from scanned PDF using improved OCR."""
    def log(message: str):
        print(message)
        if progress_callback:
            progress_callback(message)
            
    log(f"\n🔍 Processing PDF with OCR: {Path(pdf_path).name}")
    
    # Verify Tesseract before processing
    if not verify_tesseract():
        return ""
    
    try:
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        log(f"Starting OCR processing of {total_pages} pages...")
        
        full_text = ""
        total_chars = 0
        processed_pages = 0
        
        # Process each page
        for page_num in range(total_pages):
            try:
                # Get page
                page = doc[page_num]
                
                # Convert page to image with much higher DPI
                pix = page.get_pixmap(matrix=fitz.Matrix(4, 4))  # 4x zoom for better quality
                img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, pix.n)
                
                if pix.n == 4:  # RGBA
                    img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2BGR)
                elif pix.n == 3:  # RGB
                    img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
                
                # Enhanced preprocessing
                processed_image = preprocess_image(img_array)
                
                # Improved Tesseract config
                custom_config = (
                    '--oem 3 '  # OCR Engine Mode
                    '--psm 6 '  # Page Segmentation Mode
                    '-l eng+deu '  # Languages
                    '--dpi 600 '  # Higher DPI
                    '-c tessedit_char_blacklist=§±†‡©®™ '  # Remove special chars
                    '-c tessedit_do_invert=0 '  # Don't invert colors
                    '-c textord_heavy_nr=0 '  # Improve number recognition
                    '-c textord_min_linesize=2.5'  # Better line detection
                )
                
                page_text = pytesseract.image_to_string(processed_image, config=custom_config)
                page_text = clean_ocr_text(page_text)
                
                if len(page_text.strip()) > 10:
                    processed_pages += 1
                    total_chars += len(page_text)
                    log(f"OCR Progress: {processed_pages}/{total_pages} pages ({total_chars} chars total)")
                    full_text += page_text + "\n\n"
                else:
                    log(f"⚠️ Low quality text on page {page_num + 1}, skipping")
                    
            except Exception as e:
                log(f"❌ Error in page {page_num + 1}: {str(e)}")
                continue
        
        doc.close()
        
        if not full_text.strip():
            log("❌ No usable text extracted")
            return ""
            
        log(f"✅ OCR complete: {processed_pages}/{total_pages} pages processed")
        log(f"📄 Total characters extracted: {total_chars}")
        return full_text.strip()
        
    except Exception as e:
        log(f"❌ Error in OCR processing: {str(e)}")
        return ""

# Remove the automatic execution code
# if __name__ == "__main__":
#     pdf_path = r"C:\Users\<USER>\Documents\VSCode\JobScraper\Embedding\HvH_Zeugnisse.pdf"
#     extracted_text = extract_text_from_scanned_pdf(pdf_path)
#     with open("HvH_Zeugnisse_extracted.txt", "w", encoding="utf-8") as f:
#         f.write(extracted_text)
#     print("Text extraction complete. Saved as HvH_Zeugnisse_extracted.txt")
