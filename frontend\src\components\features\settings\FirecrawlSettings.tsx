'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Button,
  Text,
  VStack,
  Heading,
  Flex,
  useToast,
  Alert,
  AlertIcon,
  Card,
  CardHeader,
  CardBody,
  Divider,
  Spinner,
  Badge,
  Switch,
  FormControl,
  FormLabel,
  HStack,
} from '@chakra-ui/react';
import { FiRefreshCw, <PERSON>Check, FiAlertTriangle } from 'react-icons/fi';

interface FirecrawlSettingsProps {
  onScrapeComplete?: () => void;
}

const FirecrawlSettings: React.FC<FirecrawlSettingsProps> = ({ onScrapeComplete }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [scrapeResult, setScrapeResult] = useState<any>(null);
  const [useFirecrawl, setUseFirecrawl] = useState<boolean>(true);
  const toast = useToast();

  // Handle scraping job URLs with Firecrawl
  const handleScrapeJobs = async () => {
    setIsLoading(true);
    setError(null);
    setScrapeResult(null);

    try {
      const response = await fetch('http://localhost:8000/api/v1/firecrawl/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to scrape jobs: ${response.status}`);
      }

      const data = await response.json();
      setScrapeResult(data);

      toast({
        title: 'Jobs Scraped',
        description: `Successfully scraped ${data.job_count} jobs from ${data.url_count} URLs`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Notify parent component if callback provided
      if (onScrapeComplete) {
        onScrapeComplete();
      }
    } catch (err) {
      console.error('Error scraping jobs:', err);
      setError(err instanceof Error ? err.message : 'Failed to scrape jobs');

      // Use mock data as fallback when backend is not available
      const mockScrapeResult = {
        job_count: 16,
        url_count: 3,
        success: true,
        timestamp: new Date().toISOString()
      };

      setScrapeResult(mockScrapeResult);

      toast({
        title: 'Using Mock Data',
        description: 'Backend not available. Using mock data for demonstration purposes.',
        status: 'warning',
        duration: 5000,
        isClosable: true,
      });

      // Notify parent component if callback provided
      if (onScrapeComplete) {
        onScrapeComplete();
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card variant="outline" mb={4}>
      <CardHeader>
        <Flex justify="space-between" align="center">
          <Heading size="md">Firecrawl Job Scraping</Heading>
          <FormControl display="flex" alignItems="center" width="auto">
            <FormLabel htmlFor="use-firecrawl" mb="0" mr={2}>
              Enable
            </FormLabel>
            <Switch
              id="use-firecrawl"
              isChecked={useFirecrawl}
              onChange={(e) => setUseFirecrawl(e.target.checked)}
              colorScheme="blue"
            />
          </FormControl>
        </Flex>
      </CardHeader>

      <Divider />

      <CardBody>
        <VStack align="stretch" spacing={4}>
          <Text>
            Firecrawl is an AI-powered web scraping service that can extract job listings from
            websites. Use this feature to scrape job URLs from the job_url.md file.
          </Text>

          <Button
            colorScheme="blue"
            onClick={handleScrapeJobs}
            isLoading={isLoading}
            isDisabled={!useFirecrawl}
            leftIcon={<FiRefreshCw />}
            width="full"
          >
            Scrape Job URLs with Firecrawl
          </Button>

          {error && (
            <Alert status="error">
              <AlertIcon />
              {error}
            </Alert>
          )}

          {isLoading && (
            <Flex justify="center" align="center" py={4}>
              <Spinner size="md" mr={2} />
              <Text>Scraping jobs...</Text>
            </Flex>
          )}

          {scrapeResult && (
            <Box borderWidth="1px" borderRadius="md" p={3}>
              <Flex justify="space-between" align="center" mb={2}>
                <Heading size="sm">Scrape Results</Heading>
                <Badge colorScheme="green">
                  <Flex align="center">
                    <FiCheck />
                    <Text ml={1}>Complete</Text>
                  </Flex>
                </Badge>
              </Flex>
              <HStack spacing={4} mt={2}>
                <Badge colorScheme="blue">{scrapeResult.job_count} Jobs</Badge>
                <Badge colorScheme="purple">{scrapeResult.url_count} URLs</Badge>
              </HStack>
            </Box>
          )}

          <Alert status="info">
            <AlertIcon as={FiAlertTriangle} />
            <Box>
              <Text fontWeight="bold">Important:</Text>
              <Text>
                Firecrawl requires an API key to be set in your .env file. Make sure
                FIRECRAWL_API_KEY is properly configured.
              </Text>
            </Box>
          </Alert>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default FirecrawlSettings;
