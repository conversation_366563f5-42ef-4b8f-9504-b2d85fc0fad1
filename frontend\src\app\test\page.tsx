'use client';

import React, { useState, useEffect } from 'react';
import { Box, Heading, Text, VStack, Alert, AlertIcon, Spinner, Container } from '@chakra-ui/react';

export default function TestPage() {
  const [jobUrls, setJobUrls] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await fetch('http://localhost:8000/api/v1/job-url-md');
        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }
        const data = await response.json();
        console.log('Received data:', data);
        setJobUrls(data);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    }

    fetchData();
  }, []);

  return (
    <Container maxW="container.lg" py={8}>
      <Heading mb={4}>Simple Test Page</Heading>
      <Text mb={6}>This is a simplified page to test API connectivity.</Text>

      {isLoading ? (
        <Box textAlign="center" py={10}>
          <Spinner size="xl" />
          <Text mt={4}>Loading job URLs...</Text>
        </Box>
      ) : error ? (
        <Alert status="error">
          <AlertIcon />
          {error}
        </Alert>
      ) : (
        <VStack align="stretch" spacing={4}>
          <Heading size="md">Job URLs ({jobUrls.length})</Heading>
          {jobUrls.map((item) => (
            <Box key={item.id} p={4} borderWidth="1px" borderRadius="md">
              <Text fontWeight="bold">ID: {item.id}</Text>
              <Text>URL: {item.url}</Text>
              <Text>Prefix: {item.prefix}</Text>
            </Box>
          ))}
        </VStack>
      )}
    </Container>
  );
}
