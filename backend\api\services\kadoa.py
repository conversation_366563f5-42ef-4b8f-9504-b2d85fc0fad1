from typing import List, Dict, Optional
import httpx
import logging
from fastapi import HTTPException
from datetime import datetime

from ..core.config import settings
from ..schemas.kadoa import WorkflowCreate, WorkflowResponse, JobResponse

logger = logging.getLogger(__name__)


class KadoaService:
    def __init__(self):
        """Initialize Kadoa service with API configuration"""
        self.api_key = settings.KADOA_API_KEY
        self.api_url = settings.KADOA_API_URL
        self.headers = {
            "accept": "application/json",
            "x-api-key": self.api_key
        }
        logger.info(f"Initialized KadoaService with API URL: {self.api_url}")
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict:
        """Make HTTP request to Kadoa API"""
        url = f"{self.api_url}{endpoint}"
        logger.debug(f"Making {method} request to {url}")
        
        try:
            async with httpx.AsyncClient() as client:
                logger.debug(f"Request headers: {self.headers}")
                logger.debug(f"Request kwargs: {kwargs}")
                
                response = await client.request(
                    method=method,
                    url=url,
                    headers=self.headers,
                    **kwargs
                )
                
                logger.debug(f"Response status: {response.status_code}")
                logger.debug(f"Response headers: {response.headers}")
                
                # Handle common error cases
                if response.status_code == 404:
                    raise HTTPException(status_code=404, detail="Resource not found")
                elif response.status_code == 401:
                    raise HTTPException(status_code=401, detail="Invalid API key")
                elif response.status_code == 403:
                    raise HTTPException(status_code=403, detail="Access forbidden")
                
                try:
                    response.raise_for_status()
                    data = response.json()
                    logger.debug(f"Response data: {data}")
                    return data
                except httpx.HTTPError as e:
                    logger.error(f"HTTP error in response: {str(e)}")
                    raise
                except ValueError as e:
                    logger.error(f"Invalid JSON in response: {str(e)}")
                    raise HTTPException(status_code=500, detail="Invalid response from Kadoa API")
                
        except httpx.HTTPError as e:
            logger.error(f"Error connecting to Kadoa API: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error connecting to Kadoa API: {str(e)}")
    
    def _transform_workflow(self, workflow: Dict) -> Dict:
        """Transform workflow data to match our schema"""
        return {
            "_id": workflow.get("_id"),
            "url": workflow.get("url"),
            "displayState": workflow.get("displayState", "PENDING"),
            "createdAt": workflow.get("createdAt", datetime.utcnow()),
            "updatedAt": workflow.get("updatedAt", datetime.utcnow()),
            "name": workflow.get("name"),
            "schema_type": workflow.get("schema_type", "DETAIL")
        }
    
    async def list_workflows(self) -> List[WorkflowResponse]:
        """Get all available workflows"""
        logger.info("Fetching workflows from Kadoa API")
        try:
            data = await self._make_request("GET", "/workflows")
            workflows = data.get("workflows", [])
            logger.info(f"Successfully fetched {len(workflows)} workflows")
            logger.debug(f"Raw workflows data: {workflows}")
            
            transformed_workflows = [self._transform_workflow(w) for w in workflows]
            logger.debug(f"Transformed workflows: {transformed_workflows}")
            
            return [WorkflowResponse.model_validate(w) for w in transformed_workflows]
        except Exception as e:
            logger.error(f"Error fetching workflows: {str(e)}")
            raise
    
    async def get_workflow_jobs(self, workflow_id: str, page: int = 1, limit: int = 100) -> List[JobResponse]:
        """Get jobs for a specific workflow"""
        params = {
            "format": "json",
            "page": page,
            "limit": limit
        }
        
        data = await self._make_request(
            "GET",
            f"/workflows/{workflow_id}/data",
            params=params
        )
        
        jobs = data.get("data", [])
        return [JobResponse(**job) for job in jobs]
    
    async def create_workflow(self, workflow: WorkflowCreate) -> WorkflowResponse:
        """Create a new workflow"""
        data = await self._make_request(
            "POST",
            "/workflows",
            json=workflow.model_dump()
        )
        return WorkflowResponse(**data)
    
    async def get_workflow_status(self, workflow_id: str) -> WorkflowResponse:
        """Get workflow status"""
        data = await self._make_request("GET", f"/workflows/{workflow_id}")
        return WorkflowResponse(**data)
    
    async def delete_workflow(self, workflow_id: str) -> bool:
        """Delete a workflow"""
        await self._make_request("DELETE", f"/workflows/{workflow_id}")
        return True
    
    async def update_workflow(self, workflow_id: str, workflow: WorkflowCreate) -> WorkflowResponse:
        """Update a workflow"""
        data = await self._make_request(
            "PUT",
            f"/workflows/{workflow_id}",
            json=workflow.model_dump()
        )
        return WorkflowResponse(**data) 