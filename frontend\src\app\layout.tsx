'use client';

import React from 'react';
import { ChakraProvider, ColorModeScript, Box, Text } from '@chakra-ui/react';
import theme from '../styles/theme';
import { ErrorBoundary } from '../components/common';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <title>JoMaDe - Job Market Detector</title>
        <meta name="description" content="Job Market Detector for the scattered job market" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body>
        <ColorModeScript initialColorMode={theme.config.initialColorMode} />
        <ChakraProvider theme={theme}>
          <ErrorBoundary
            fallback={
              <Box p={8} textAlign="center">
                <Text fontSize="xl">Something went wrong loading the application.</Text>
                <Text mt={4}>Please refresh the page to try again.</Text>
              </Box>
            }
          >
            {children}
          </ErrorBoundary>
        </ChakraProvider>
      </body>
    </html>
  );
}
