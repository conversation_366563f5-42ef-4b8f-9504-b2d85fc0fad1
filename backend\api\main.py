from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
import time
from sqlalchemy.orm import Session
from .core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.DEBUG if settings.DEBUG else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

logger.info(f"Starting application with settings: DEBUG={settings.DEBUG}")

# Create necessary directories using standardized paths from settings
directories = [
    settings.DATA_DIR,
    settings.EMBEDDINGS_DIR,
    settings.DOCUMENTS_DIR,
    settings.JOBS_DIR
]

for directory in directories:
    os.makedirs(directory, exist_ok=True)
    logger.info(f"Created directory: {directory}")

# Initialize database
from .db.init_db import init_db
from .db.session import get_db

try:
    logger.info("Initializing database...")
    init_db()
    logger.info("Database initialized successfully")
except Exception as e:
    logger.error(f"Error initializing database: {str(e)}")
    # Continue execution - we'll handle database errors in the health check

app = FastAPI(
    title="JoMaDe API",
    description="Job Market Detector API",
    version=settings.VERSION
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=settings.ALLOWED_METHODS,
    allow_headers=settings.ALLOWED_HEADERS,
)

# Add error handling middleware
from .middleware.error_handler import ErrorHandler
app.middleware("http")(ErrorHandler())

logger.info(f"Configured CORS with origins: {settings.ALLOWED_ORIGINS}")
logger.info("Added error handling middleware")

# Include routers
from .routes import kadoa, cv, jobs, documents, job_sources, embeddings, cv_md, job_url_md, firecrawl, simple
app.include_router(kadoa.router)
app.include_router(cv.router)
app.include_router(jobs.router)
app.include_router(documents.router)
app.include_router(job_sources.router)
app.include_router(embeddings.router)
app.include_router(cv_md.router)
app.include_router(job_url_md.router)
app.include_router(firecrawl.router)
app.include_router(simple.router)  # Add the simplified API router

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to JoMaDe API",
        "version": settings.VERSION,
        "docs_url": "/docs"
    }

@app.get(f"{settings.API_V1_STR}")
async def api_v1_root():
    """API v1 root endpoint"""
    return {
        "message": "Welcome to JoMaDe API v1",
        "version": settings.VERSION,
        "endpoints": [
            "jobs",
            "cv",
            "documents",
            "job_sources",
            "embeddings",
            "kadoa",
            "firecrawl"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    from .db.session import engine
    from .db.init_db import get_table_info

    health_status = {
        "status": "healthy",
        "api_version": settings.VERSION,
        "database": {
            "connected": False,
            "type": "unknown",
            "tables": {}
        }
    }

    # Check database connection
    try:
        # Test connection
        with engine.connect() as connection:
            health_status["database"]["connected"] = True

            # Get database type
            if settings.DATABASE_URL.startswith("sqlite"):
                health_status["database"]["type"] = "sqlite"
            elif settings.DATABASE_URL.startswith("postgresql"):
                health_status["database"]["type"] = "postgresql"
            else:
                health_status["database"]["type"] = str(engine.dialect.name)

            # Get table info
            health_status["database"]["tables"] = get_table_info()
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        health_status["status"] = "unhealthy"
        health_status["database"]["error"] = str(e)

    return health_status