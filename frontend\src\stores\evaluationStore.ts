import { create } from 'zustand';

interface EvaluationResult {
  id: string;
  title: string;
  score: number;
  shortlistScore?: number;
  pros: string;
  cons: string;
  color: 'green' | 'orange' | 'red';
}

interface EvaluationState {
  results: EvaluationResult[];
  isLoading: boolean;
  error: string | null;
  temperature: number;
  threshold: number;

  // Counts
  counts: {
    total: number;
    green: number;
    orange: number;
    red: number;
  };

  // Actions
  setResults: (results: EvaluationResult[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setTemperature: (temperature: number) => void;
  setThreshold: (threshold: number) => void;

  // API calls
  evaluateJobs: (shortlistedJobIds: string[]) => Promise<EvaluationResult[]>;
}

export const useEvaluationStore = create<EvaluationState>((set, get) => ({
  results: [],
  isLoading: false,
  error: null,
  temperature: 0.7,
  threshold: 70,

  counts: {
    total: 0,
    green: 0,
    orange: 0,
    red: 0,
  },

  // Actions
  setResults: results => {
    const counts = {
      total: results.length,
      green: results.filter(r => r.color === 'green').length,
      orange: results.filter(r => r.color === 'orange').length,
      red: results.filter(r => r.color === 'red').length,
    };

    set({ results, counts });
  },

  setLoading: loading => set({ isLoading: loading }),
  setError: error => set({ error }),
  setTemperature: temperature => set({ temperature }),
  setThreshold: threshold => set({ threshold }),

  // API calls
  evaluateJobs: async shortlistedJobIds => {
    const { temperature, threshold } = get();
    set({ isLoading: true, error: null });

    try {
      const response = await fetch('http://localhost:8000/api/v1/jobs/evaluate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          job_ids: shortlistedJobIds,
          temperature,
          threshold,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to evaluate jobs: ${response.status}`);
      }

      const data = await response.json();

      // Transform the results to match our EvaluationResult interface
      const transformedResults: EvaluationResult[] = data.evaluated_jobs.map((job: any) => ({
        id: job.id,
        title: job.title,
        score: job.fit_percentage,
        shortlistScore: job.shortlist_score,
        pros: job.pros,
        cons: job.cons,
        color: job.fit_percentage >= 80 ? 'green' : job.fit_percentage >= 60 ? 'orange' : 'red',
      }));

      const counts = {
        total: transformedResults.length,
        green: transformedResults.filter(r => r.color === 'green').length,
        orange: transformedResults.filter(r => r.color === 'orange').length,
        red: transformedResults.filter(r => r.color === 'red').length,
      };

      set({
        results: transformedResults,
        counts,
        isLoading: false,
      });

      return transformedResults;
    } catch (err) {
      console.error('Error evaluating jobs:', err);
      set({
        error: err instanceof Error ? err.message : 'Failed to evaluate jobs',
        isLoading: false,
      });
      return [];
    }
  },
}));
