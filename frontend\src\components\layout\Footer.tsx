'use client';

import React from 'react';
import {
  Box,
  Container,
  Stack,
  Text,
  Link,
  useColorModeValue,
  Flex,
  Divider,
  HStack,
} from '@chakra-ui/react';
import { FiGithub, FiHelpCircle } from 'react-icons/fi';

const Footer: React.FC = () => {
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box as="footer" mt="8" py="4" borderTop="1px" borderTopColor={borderColor}>
      <Container maxW="container.xl">
        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align="center">
          <Text fontSize="sm" color={textColor}>
            &copy; {new Date().getFullYear()} JoMaDe. All rights reserved.
          </Text>

          <HStack spacing={4} mt={{ base: 4, md: 0 }}>
            <Link href="#" fontSize="sm" color={textColor} display="flex" alignItems="center">
              <FiHelpCircle size="14px" style={{ marginRight: '6px' }} />
              Help
            </Link>
            <Link href="#" fontSize="sm" color={textColor} display="flex" alignItems="center">
              <FiGithub size="14px" style={{ marginRight: '6px' }} />
              GitHub
            </Link>
            <Text fontSize="sm" color={textColor}>
              Version 0.1.0
            </Text>
          </HStack>
        </Flex>
      </Container>
    </Box>
  );
};

export default Footer;
