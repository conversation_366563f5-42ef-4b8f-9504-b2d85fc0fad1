import os
import sys
import json
import logging
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import required libraries
try:
    import openai
    from langchain.embeddings import OpenAIEmbeddings
    from langchain.vectorstores import FAISS
    from langchain.llms import OpenAI
    from langchain.chains import LLMChain
    from langchain.prompts import PromptTemplate
except ImportError as e:
    logger.error(f"Required libraries not installed: {str(e)}")
    logger.error("Please install required libraries: pip install langchain openai faiss-cpu")
    raise

def rag_shortlist(
    jobs_dict: Dict[str, Any],
    faiss_path: str,
    method: str = "faiss_preselect",
    faiss_threshold: float = 0.3,
    temperature: float = 0.7,
    max_jobs: int = 50
) -> List[Tuple[str, float]]:
    """
    Shortlist jobs using RAG (Retrieval Augmented Generation)
    
    Args:
        jobs_dict: Dictionary of jobs
        faiss_path: Path to FAISS index
        method: Method to use ('faiss_preselect' or 'full_llm')
        faiss_threshold: Threshold for FAISS similarity
        temperature: Temperature for LLM
        max_jobs: Maximum number of jobs to process
        
    Returns:
        List of tuples (job_id, score)
    """
    logger.info(f"RAG shortlisting jobs using method={method}, faiss_threshold={faiss_threshold}")
    
    # Check if FAISS index exists
    if not os.path.exists(faiss_path):
        logger.error(f"FAISS index not found at {faiss_path}")
        raise FileNotFoundError(f"FAISS index not found at {faiss_path}")
    
    # Load FAISS index
    embeddings = OpenAIEmbeddings()
    vectorstore = FAISS.load_local(faiss_path, embeddings)
    logger.info(f"Loaded FAISS index from {faiss_path}")
    
    # Get all documents from FAISS index
    all_docs = vectorstore.similarity_search_with_score("", k=100)
    cv_text = "\n\n".join([doc[0].page_content for doc in all_docs])
    logger.info(f"Extracted CV text from {len(all_docs)} documents")
    
    # Prepare jobs for processing
    jobs_to_process = list(jobs_dict.items())[:max_jobs]
    logger.info(f"Processing {len(jobs_to_process)} jobs")
    
    shortlisted_jobs = []
    
    if method == "faiss_preselect":
        # Use FAISS to pre-select jobs
        logger.info(f"Using FAISS pre-selection with threshold {faiss_threshold}")
        
        # Create job descriptions for embedding
        job_descriptions = []
        job_ids = []
        
        for job_id, job_data in jobs_to_process:
            description = f"{job_data.get('title', '')} - {job_data.get('company', '')}\n{job_data.get('description', '')}"
            job_descriptions.append(description)
            job_ids.append(job_id)
        
        # Get embeddings for job descriptions
        job_embeddings = embeddings.embed_documents(job_descriptions)
        
        # Get embeddings for CV documents
        cv_embeddings = []
        for doc, _ in all_docs:
            cv_embeddings.append(embeddings.embed_query(doc.page_content))
        
        # Calculate similarity between job descriptions and CV documents
        from numpy import dot
        from numpy.linalg import norm
        
        # Function to calculate cosine similarity
        def cosine_similarity(a, b):
            return dot(a, b) / (norm(a) * norm(b))
        
        # Calculate similarity for each job
        for i, job_id in enumerate(job_ids):
            job_embedding = job_embeddings[i]
            
            # Calculate max similarity with any CV document
            max_similarity = 0
            for cv_embedding in cv_embeddings:
                similarity = cosine_similarity(job_embedding, cv_embedding)
                max_similarity = max(max_similarity, similarity)
            
            # If similarity is above threshold, shortlist job
            if max_similarity >= faiss_threshold:
                # Convert similarity to percentage score
                score = int(max_similarity * 100)
                shortlisted_jobs.append((job_id, score))
                logger.info(f"Shortlisted job {job_id} with score {score}")
        
        # Sort by score descending
        shortlisted_jobs.sort(key=lambda x: x[1], reverse=True)
        
    else:  # full_llm
        # Use LLM to evaluate all jobs
        logger.info(f"Using full LLM evaluation with temperature {temperature}")
        
        # Create LLM
        llm = OpenAI(temperature=temperature)
        
        # Create prompt template
        template = """
        You are an expert job matcher. Your task is to evaluate if a job is a good match for a candidate based on their CV.
        
        CV:
        {cv_text}
        
        Job:
        {job_description}
        
        Evaluate if this job is a good match for the candidate. Consider skills, experience, and qualifications.
        First, analyze the key requirements of the job.
        Second, check if the candidate's CV demonstrates these requirements.
        Finally, provide a match percentage (0-100) and briefly explain your reasoning.
        
        Output format:
        Match Percentage: [0-100]
        Reasoning: [Your explanation]
        """
        
        prompt = PromptTemplate(
            input_variables=["cv_text", "job_description"],
            template=template
        )
        
        # Create chain
        chain = LLMChain(llm=llm, prompt=prompt)
        
        # Process each job
        for job_id, job_data in jobs_to_process:
            job_description = f"{job_data.get('title', '')} - {job_data.get('company', '')}\n{job_data.get('description', '')}"
            
            try:
                # Run chain
                result = chain.run(cv_text=cv_text, job_description=job_description)
                
                # Extract match percentage
                import re
                match = re.search(r"Match Percentage: (\d+)", result)
                
                if match:
                    score = int(match.group(1))
                    
                    # If score is above threshold, shortlist job
                    if score >= 50:  # Use 50% as minimum threshold
                        shortlisted_jobs.append((job_id, score))
                        logger.info(f"Shortlisted job {job_id} with score {score}")
                else:
                    logger.warning(f"Could not extract match percentage for job {job_id}")
                
            except Exception as e:
                logger.error(f"Error processing job {job_id}: {str(e)}")
        
        # Sort by score descending
        shortlisted_jobs.sort(key=lambda x: x[1], reverse=True)
    
    logger.info(f"Shortlisted {len(shortlisted_jobs)} jobs")
    return shortlisted_jobs
