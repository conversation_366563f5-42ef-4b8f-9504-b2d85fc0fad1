import logging
import os
from sqlalchemy.orm import Session
from sqlalchemy import inspect
from sqlalchemy.exc import SQLAlchemyError

from .base import Base
from .session import engine, logger as session_logger
from ..models import job, user, kadoa  # Import all models to ensure they're registered with Base

logger = logging.getLogger(__name__)

def init_db() -> None:
    """Initialize the database by creating all tables."""
    try:
        # Check if database exists and has tables
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()

        if existing_tables:
            logger.info(f"Database already contains tables: {', '.join(existing_tables)}")
            return

        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")

    except SQLAlchemyError as e:
        logger.error(f"SQLAlchemy error creating database tables: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating database tables: {str(e)}")
        raise

def get_table_info() -> dict:
    """Get information about the database tables."""
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()

        table_info = {}
        for table in tables:
            columns = inspector.get_columns(table)
            table_info[table] = {
                "column_count": len(columns),
                "columns": [col["name"] for col in columns]
            }

            # Try to get row count (may not work for all database types)
            try:
                with engine.connect() as conn:
                    result = conn.execute(f"SELECT COUNT(*) FROM {table}")
                    row_count = result.scalar()
                    table_info[table]["row_count"] = row_count
            except Exception as e:
                logger.warning(f"Could not get row count for table {table}: {str(e)}")
                table_info[table]["row_count"] = "Unknown"

        return table_info
    except Exception as e:
        logger.error(f"Error getting table info: {str(e)}")
        return {"error": str(e)}