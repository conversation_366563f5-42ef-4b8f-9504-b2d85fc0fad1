

import fitz  # PyMuPDF
import requests

def scrape_troeger_cie(url, index):
    """Scrape job listings from troeger-cie.de"""
    try:
        response = requests.get(url)
        response.raise_for_status()
        with open("temp.pdf", "wb") as f:
            f.write(response.content)

        # Open the PDF file
        pdf_document = fitz.open("temp.pdf")
        text = ""
        for page_num in range(len(pdf_document)):
            page = pdf_document.load_page(page_num)
            text += page.get_text()

        # Process the text to extract job information
        job_info = {
            'index': index,
            'job_description': text
        }

        print(f"Extracted text from PDF {index}: {text[:100]}...")  # Debugging statement
        return job_info
    except Exception as e:
        print(f"Error extracting PDF: {e}")
        return {}
    