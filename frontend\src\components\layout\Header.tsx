'use client';

import React from 'react';
import {
  Box,
  Flex,
  Text,
  IconButton,
  Button,
  Stack,
  Collapse,
  Icon,
  Popover,
  PopoverTrigger,
  PopoverContent,
  useColorModeValue,
  useBreakpointValue,
  useDisclosure,
  HStack,
  Avatar,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
  Badge,
  Spacer,
  Heading,
} from '@chakra-ui/react';
import {
  FiMenu,
  FiChevronDown,
  FiUser,
  FiSettings,
  FiLogOut,
  FiBell,
  FiSearch,
} from 'react-icons/fi';

interface HeaderProps {
  onMenuClick: () => void;
  title?: string;
}

const Header: React.FC<HeaderProps> = ({ onMenuClick, title = 'JoMaDe' }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <Box
      as="header"
      position="sticky"
      top="0"
      zIndex="1000"
      bg={bgColor}
      borderBottom="1px"
      borderBottomColor={borderColor}
      px={{ base: 4, md: 6 }}
      py={3}
      shadow="sm"
    >
      <Flex alignItems="center" justifyContent="space-between">
        {/* Left section with menu button and logo */}
        <HStack spacing={4}>
          {isMobile && (
            <IconButton
              aria-label="Open menu"
              icon={<FiMenu />}
              variant="ghost"
              onClick={onMenuClick}
              size="md"
            />
          )}

          <Heading
            as="h1"
            size="md"
            fontWeight="bold"
            bgGradient="linear(to-r, blue.400, teal.400)"
            bgClip="text"
          >
            {title}
          </Heading>
        </HStack>

        <Spacer />

        {/* Right section with notifications and user menu */}
        <HStack spacing={4}>
          <IconButton aria-label="Search" icon={<FiSearch />} variant="ghost" size="md" />

          <Box position="relative">
            <IconButton aria-label="Notifications" icon={<FiBell />} variant="ghost" size="md" />
            <Badge
              position="absolute"
              top="-2px"
              right="-2px"
              colorScheme="red"
              borderRadius="full"
              size="xs"
            >
              3
            </Badge>
          </Box>

          <Menu>
            <MenuButton as={Button} variant="ghost" rightIcon={<FiChevronDown />} size="sm">
              <HStack>
                <Avatar size="sm" name="User" src="/avatar-placeholder.png" />
                <Text display={{ base: 'none', md: 'block' }}>User</Text>
              </HStack>
            </MenuButton>
            <MenuList>
              <MenuItem icon={<FiUser />}>Profile</MenuItem>
              <MenuItem icon={<FiSettings />}>Settings</MenuItem>
              <MenuDivider />
              <MenuItem icon={<FiLogOut />}>Sign Out</MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </Flex>
    </Box>
  );
};

export default Header;
