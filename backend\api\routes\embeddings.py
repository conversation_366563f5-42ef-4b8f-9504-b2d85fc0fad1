from fastapi import APIRouter, HTTPException, Depends, File, UploadFile, Form, Body
from typing import Dict, Any, List, Optional
import os
import sys
import json
import shutil
import datetime
from ..core.config import settings
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix=f"{settings.API_V1_STR}/embeddings",
    tags=["embeddings"],
    responses={404: {"description": "Not found"}},
)

# Path to embeddings directory
EMBEDDINGS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))), "data", "embeddings")

# Create embeddings directory if it doesn't exist
os.makedirs(EMBEDDINGS_DIR, exist_ok=True)

# Add the processing directory to the Python path
PROCESSING_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "core", "processing")
if PROCESSING_DIR not in sys.path:
    sys.path.insert(0, PROCESSING_DIR)

@router.get("/info")
async def get_embedding_info():
    """
    Get information about the current embedding
    """
    try:
        logger.info("Getting embedding info")
        
        # Check if embeddings directory exists
        if not os.path.exists(EMBEDDINGS_DIR):
            logger.warning("Embeddings directory not found")
            raise HTTPException(status_code=404, detail="No embeddings found")
        
        # Look for FAISS and JSON files
        faiss_files = [f for f in os.listdir(EMBEDDINGS_DIR) if f.endswith('.faiss')]
        json_files = [f for f in os.listdir(EMBEDDINGS_DIR) if f.endswith('.json')]
        
        if not faiss_files:
            logger.warning("No FAISS files found")
            raise HTTPException(status_code=404, detail="No embeddings found")
        
        # Use the most recent file's timestamp
        all_files = faiss_files + json_files
        latest_file = max(all_files, key=lambda f: os.path.getmtime(os.path.join(EMBEDDINGS_DIR, f)))
        
        # Try to get info from JSON if available, otherwise use FAISS filename
        embedding_info = {}
        if json_files:
            latest_json = max(json_files, key=lambda f: os.path.getmtime(os.path.join(EMBEDDINGS_DIR, f)))
            with open(os.path.join(EMBEDDINGS_DIR, latest_json)) as f:
                data = json.load(f)
                embedding_info = {
                    'date': data.get('date', ''),
                    'model': data.get('model', ''),
                    'user': data.get('user', 'User'),
                    'file_count': len(data.get('files', [])),
                    'filename': latest_file
                }
        else:
            # Create info from FAISS filename
            latest_faiss = max(faiss_files, key=lambda f: os.path.getmtime(os.path.join(EMBEDDINGS_DIR, f)))
            date_str = latest_faiss.split('_')[0]  # Extract date from filename
            embedding_info = {
                'date': date_str,
                'model': "text-embedding-ada-002",  # Default model
                'user': 'User',
                'file_count': 1,
                'filename': latest_faiss
            }
        
        logger.info(f"Found embedding info: {embedding_info}")
        return embedding_info
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error getting embedding info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting embedding info: {str(e)}")

@router.post("/create")
async def create_embedding(
    files: List[UploadFile] = File(...),
    model: str = Form("text-embedding-ada-002")
):
    """
    Create embedding from uploaded files
    """
    try:
        logger.info(f"Creating embedding with model: {model}")
        
        # Create embeddings directory if it doesn't exist
        os.makedirs(EMBEDDINGS_DIR, exist_ok=True)
        
        # Create temporary directory for uploaded files
        temp_dir = os.path.join(EMBEDDINGS_DIR, "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        # Save uploaded files to temporary directory
        file_paths = []
        for file in files:
            file_path = os.path.join(temp_dir, file.filename)
            with open(file_path, "wb") as f:
                shutil.copyfileobj(file.file, f)
            file_paths.append(file_path)
            logger.info(f"Saved file: {file.filename}")
        
        # Import the embedding module
        try:
            # Try to import from the processing directory
            from embedding_creator import create_embedding
        except ImportError:
            # If that fails, try to import using importlib
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                "embedding_creator",
                os.path.join(PROCESSING_DIR, "embedding_creator.py")
            )
            embedding_creator = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(embedding_creator)
            create_embedding = embedding_creator.create_embedding
        
        # Create embedding
        date_str = datetime.datetime.now().strftime("%Y%m%d")
        embedding_name = f"{date_str}_User_embedding"
        
        # Create embedding
        faiss_path, json_path = create_embedding(
            file_paths=file_paths,
            embedding_name=embedding_name,
            model=model,
            output_dir=EMBEDDINGS_DIR
        )
        
        # Create embedding info
        embedding_info = {
            'date': date_str,
            'model': model,
            'user': 'User',
            'file_count': len(file_paths),
            'filename': os.path.basename(faiss_path)
        }
        
        # Clean up temporary directory
        for file_path in file_paths:
            if os.path.exists(file_path):
                os.remove(file_path)
        
        if os.path.exists(temp_dir):
            os.rmdir(temp_dir)
        
        logger.info(f"Successfully created embedding: {embedding_info}")
        return embedding_info
        
    except Exception as e:
        logger.error(f"Error creating embedding: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating embedding: {str(e)}")

@router.delete("/delete")
async def delete_embedding():
    """
    Delete existing embedding
    """
    try:
        logger.info("Deleting embedding")
        
        # Check if embeddings directory exists
        if not os.path.exists(EMBEDDINGS_DIR):
            logger.warning("Embeddings directory not found")
            raise HTTPException(status_code=404, detail="No embeddings found")
        
        # Look for FAISS and JSON files
        deleted = False
        for file in os.listdir(EMBEDDINGS_DIR):
            if file.endswith('.faiss') or file.endswith('.json'):
                file_path = os.path.join(EMBEDDINGS_DIR, file)
                os.remove(file_path)
                deleted = True
                logger.info(f"Deleted file: {file}")
        
        if not deleted:
            logger.warning("No embedding files found to delete")
            raise HTTPException(status_code=404, detail="No embeddings found")
        
        logger.info("Successfully deleted embedding")
        return {"message": "Successfully deleted embedding"}
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting embedding: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting embedding: {str(e)}")
