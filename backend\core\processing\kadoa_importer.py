import requests
import json
import logging
from typing import List, Dict, Optional
import os
import pandas as pd
from pathlib import Path
import sys

# Add the parent directory to sys.path to import from api.core.config
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from api.core.config import settings
except ImportError:
    # Fallback if import fails
    print("❌ Error: Could not import settings from api.core.config")
    print("Using environment variables directly (not recommended)")
    from dotenv import load_dotenv
    load_dotenv()

    class Settings:
        KADOA_API_KEY = os.getenv("KADOA_API_KEY")
        KADOA_API_URL = os.getenv("KADOA_API_URL", "https://api.kadoa.com/v4")
        JOB_SITES_EXCEL_PATH = os.getenv("JOB_SITES_EXCEL_PATH", "./job_sites.xlsx")

    settings = Settings()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get API key and URL from settings
KADOA_API_KEY = settings.KADOA_API_KEY
KADOA_API_URL = settings.KADOA_API_URL

# Debug logging for API key
logger.info("Checking Kadoa API key...")
logger.info(f"API key present: {'Yes' if KADOA_API_KEY else 'No'}")
if KADOA_API_KEY:
    logger.info(f"API key length: {len(KADOA_API_KEY)}")
    logger.info(f"First 8 chars: {KADOA_API_KEY[:8]}")
else:
    logger.error("KADOA_API_KEY not found in settings")

def get_kadoa_workflows() -> Optional[List[Dict]]:
    """Fetch all available Kadoa workflows."""
    if not KADOA_API_KEY:
        print("❌ Error: KADOA_API_KEY not found in environment variables")
        return None

    try:
        # Kadoa workflows endpoint
        url = f"{KADOA_API_URL}/v4/workflows"

        # Headers with authentication
        headers = {
            "accept": "application/json",
            "x-api-key": KADOA_API_KEY
        }

        print(f"🔄 Fetching Kadoa workflows from: {url}")
        print(f"Using API key: {KADOA_API_KEY[:8]}...")  # Print first 8 chars of API key for debugging

        # Make request to Kadoa
        response = requests.get(url, headers=headers)

        # Print response details for debugging
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response body: {response.text}")

        # Check for specific error cases
        if response.status_code == 404:
            print("❌ Error: Kadoa API endpoint not found. Please verify the API URL.")
            return None
        elif response.status_code == 401:
            print("❌ Error: Invalid or expired API key. Please check your KADOA_API_KEY.")
            return None
        elif response.status_code == 403:
            print("❌ Error: Access forbidden. Your API key may not have the required permissions.")
            return None

        response.raise_for_status()

        # Parse response
        data = response.json()
        workflows = data.get("workflows", [])

        if not workflows:
            print("⚠ No workflows returned from Kadoa API")
            return None

        print(f"✅ Successfully retrieved {len(workflows)} workflows from Kadoa API")
        return workflows

    except requests.exceptions.RequestException as e:
        print(f"❌ Error connecting to Kadoa API: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing Kadoa response: {str(e)}")
        return None

def get_kadoa_jobs(workflow_id: str) -> Optional[List[Dict]]:
    """Fetch jobs for a specific Kadoa workflow."""
    if not KADOA_API_KEY:
        print("❌ Error: KADOA_API_KEY not found in environment variables")
        return None

    try:
        # Kadoa API endpoint for jobs - updated to use /data endpoint
        url = f"{KADOA_API_URL}/v4/workflows/{workflow_id}/data"

        # Query parameters - removed sortBy and order since they're not supported
        params = {
            "format": "json",
            "page": 1,
            "limit": 100
        }

        # Headers with authentication
        headers = {
            "accept": "application/json",
            "x-api-key": KADOA_API_KEY
        }

        print(f"🔄 Fetching jobs for workflow {workflow_id}")

        # Make request to Kadoa
        response = requests.get(url, headers=headers, params=params)

        # Print response details for debugging
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response body: {response.text[:200]}...")  # Print first 200 chars of response

        # Check for specific error cases
        if response.status_code == 404:
            print(f"❌ Error: No data found for workflow {workflow_id}")
            return None
        elif response.status_code == 401:
            print("❌ Error: Invalid or expired API key")
            return None
        elif response.status_code == 403:
            print("❌ Error: Access forbidden")
            return None

        response.raise_for_status()

        # Parse response
        data = response.json()
        jobs = data.get("data", [])  # Changed from "jobs" to "data" per API spec

        if not jobs:
            print(f"⚠ No jobs returned for workflow {workflow_id}")
            return None

        print(f"✅ Retrieved {len(jobs)} jobs from workflow {workflow_id}")
        return jobs

    except requests.exceptions.RequestException as e:
        print(f"❌ Error fetching jobs for workflow {workflow_id}: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing jobs for workflow {workflow_id}: {str(e)}")
        return None

def import_all_kadoa_jobs() -> Dict[str, Dict]:
    """Import all jobs from all Kadoa workflows into the global dictionary format."""
    try:
        # Import main module to access global jobs_dict
        import main

        # First get all workflows
        workflows = get_kadoa_workflows()
        if not workflows:
            return {}

        # For each workflow, get its jobs
        workflow_counter = 0
        jobs_dict = {}

        for workflow in workflows:
            workflow_id = workflow.get("_id")
            workflow_name = workflow.get("name", "")
            workflow_state = workflow.get("state", "")
            workflow_url = workflow.get("url", "")

            if not workflow_id:
                continue

            # Get workflow prefix (A, B, C, etc.)
            workflow_prefix = chr(65 + workflow_counter)  # 65 is ASCII for 'A'
            print(f"Processing workflow {workflow_prefix}: {workflow_name} (ID: {workflow_id}, State: {workflow_state})")

            jobs = get_kadoa_jobs(workflow_id)
            if not jobs:
                continue

            # Add each job to the jobs dictionary with proper prefix indexing
            for job_counter, job in enumerate(jobs, 1):
                prefix_index = f"{workflow_prefix}{job_counter}"  # A1, A2, B1, B2, etc.

                # Get the job title from the appropriate field based on schema
                job_title = (
                    job.get('title') or
                    job.get('positionTitle') or
                    job.get('jobTitle') or
                    ''
                ).strip()

                # Get location, handling different possible field names
                location = (
                    job.get('location') or
                    job.get('landRegion') or
                    ''
                ).strip()

                # Get industry, handling different possible field names
                industry = (
                    job.get('industry') or
                    job.get('branch') or
                    job.get('category') or
                    job.get('department') or
                    ''
                ).strip()

                # Create a clean single-line display string
                display_parts = []
                if job_title:
                    display_parts.append(job_title)
                if industry:
                    display_parts.append(industry)
                if location:
                    display_parts.append(location)

                display_string = ", ".join(filter(None, display_parts))

                jobs_dict[prefix_index] = {
                    'prefixindex': prefix_index,
                    'jobtitle': job_title,
                    'location': location,
                    'industry': industry,
                    'shortlist': {'is_shortlisted': False, 'score': 0, 'timestamp': None},
                    'evaluation': {'is_evaluated': False, 'score': 0, 'pros': '', 'cons': '', 'timestamp': None},
                    'display': {
                        'color': 'none',
                        'status': 'none',
                        'text': f"{prefix_index}: {display_string}"  # Single line format
                    },
                    'job_description': '',
                    'reference': job.get('reference', ''),
                    'salary': job.get('salary', ''),
                    'remote_work': job.get('remoteWorkPossible', ''),
                    'link': job.get('link', ''),
                    'source': 'Kadoa',
                    'workflow_id': workflow_id,
                    'workflow_name': workflow_name,
                    'workflow_url': workflow_url
                }

            workflow_counter += 1

        print(f"✅ Imported {len(jobs_dict)} total jobs from Kadoa")

        # Update the global jobs_dict
        main.jobs_dict.clear()  # Clear existing entries
        main.jobs_dict.update(jobs_dict)

        return jobs_dict

    except Exception as e:
        print(f"❌ Error importing Kadoa jobs: {str(e)}")
        return {}

def create_kadoa_workflow(url: str) -> Optional[str]:
    """Create a new Kadoa workflow from URL."""
    if not KADOA_API_KEY:
        print("❌ Error: KADOA_API_KEY not found in environment variables")
        return None

    try:
        # Kadoa workflow creation endpoint
        url = f"{KADOA_API_URL}/v4/workflows"

        # Headers with authentication
        headers = {
            "accept": "application/json",
            "x-api-key": KADOA_API_KEY
        }

        # Workflow data
        data = {
            "url": url,
            "schemaType": "DETAIL"  # For job postings
        }

        # Create workflow
        response = requests.post(url, headers=headers, json=data)

        # Print response details for debugging
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response body: {response.text}")

        response.raise_for_status()

        # Get workflow ID
        workflow = response.json()
        workflow_id = workflow.get('_id')  # Changed from 'id' to '_id'

        if workflow_id:
            print(f"✅ Created Kadoa workflow: {workflow_id}")
            return workflow_id
        return None

    except requests.exceptions.RequestException as e:
        print(f"❌ Error creating Kadoa workflow: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing Kadoa response: {str(e)}")
        return None

def get_workflow_status(workflow_id: str) -> Optional[Dict]:
    """Get status of a Kadoa workflow."""
    if not KADOA_API_KEY:
        print("❌ Error: KADOA_API_KEY not found in environment variables")
        return None

    try:
        # Kadoa workflow status endpoint
        url = f"{KADOA_API_URL}/v4/workflows/{workflow_id}"

        # Headers with authentication
        headers = {
            "accept": "application/json",
            "x-api-key": KADOA_API_KEY
        }

        # Get status
        response = requests.get(url, headers=headers)

        # Print response details for debugging
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response body: {response.text}")

        response.raise_for_status()

        return response.json()

    except requests.exceptions.RequestException as e:
        print(f"❌ Error getting workflow status: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing Kadoa response: {str(e)}")
        return None

def read_workflow_ids_from_excel(excel_path: str = None) -> List[str]:
    """Read Kadoa workflow IDs from Excel file."""
    try:
        # Use the provided path or get from settings
        excel_path = excel_path or settings.JOB_SITES_EXCEL_PATH

        logger.info(f"Reading workflow IDs from Excel: {excel_path}")

        if not os.path.exists(excel_path):
            logger.error(f"Excel file not found: {excel_path}")
            return []

        df = pd.read_excel(excel_path)
        # Assuming column G (index 6) contains workflow IDs
        workflow_ids = df.iloc[:, 6].dropna().tolist()

        logger.info(f"Found {len(workflow_ids)} workflow IDs in Excel")
        return workflow_ids
    except Exception as e:
        logger.error(f"Error reading workflow IDs from Excel: {str(e)}")
        return []

def update_excel_with_workflow(url: str, workflow_id: str, excel_path: str = None) -> bool:
    """Add new workflow ID to Excel file."""
    try:
        # Use the provided path or get from settings
        excel_path = excel_path or settings.JOB_SITES_EXCEL_PATH

        logger.info(f"Updating Excel with workflow: {workflow_id} for URL: {url}")

        if not os.path.exists(excel_path):
            logger.error(f"Excel file not found: {excel_path}")
            return False

        df = pd.read_excel(excel_path)
        # Find first empty row in workflow ID column (G)
        empty_row = df.iloc[:, 6].isna().idxmax()

        # Update the row with URL and workflow ID
        df.iloc[empty_row, 7] = url  # URL column (H)
        df.iloc[empty_row, 6] = workflow_id  # Workflow ID column (G)

        # Save back to Excel
        df.to_excel(excel_path, index=False)
        logger.info(f"Successfully updated Excel with workflow: {workflow_id}")
        return True

    except Exception as e:
        logger.error(f"Error updating Excel with workflow: {str(e)}")
        return False
