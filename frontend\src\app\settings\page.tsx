'use client';

import React from 'react';
import { <PERSON>, Tabs, <PERSON>b<PERSON>ist, TabPanels, Tab, TabPanel, useColorModeValue } from '@chakra-ui/react';
import { MainLayout } from '../../components/layout';
import { PageHeader } from '../../components/common';

// Import settings components (we'll create these later)
import URLManagement from '../../components/features/settings/URLManagement';
import UserSettings from '../../components/features/settings/UserSettings';
import SystemSettings from '../../components/features/settings/SystemSettings';

const SettingsPage: React.FC = () => {
  const tabBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <MainLayout title="Settings">
      <PageHeader
        title="Settings"
        subtitle="Manage your application settings"
        breadcrumbs={[{ label: 'Home', href: '/' }, { label: 'Settings' }]}
      />

      <Box
        bg={tabBg}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
        overflow="hidden"
      >
        <Tabs variant="enclosed" colorScheme="blue" isLazy size="sm">
          <TabList bg={useColorModeValue('gray.50', 'gray.700')} px={3} pt={2}>
            <Tab _selected={{ bg: tabBg, borderBottomColor: tabBg }} fontSize="sm">URL Management</Tab>
            <Tab _selected={{ bg: tabBg, borderBottomColor: tabBg }} fontSize="sm">User Settings</Tab>
            <Tab _selected={{ bg: tabBg, borderBottomColor: tabBg }} fontSize="sm">System Settings</Tab>
          </TabList>

          <TabPanels>
            <TabPanel p={3}>
              <URLManagement />
            </TabPanel>
            <TabPanel p={3}>
              <UserSettings />
            </TabPanel>
            <TabPanel p={3}>
              <SystemSettings />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Box>
    </MainLayout>
  );
};

export default SettingsPage;
