from datetime import datetime
from typing import Optional
from uuid import uuid4

from sqlalchemy import Column, String, DateTime, Text, JSON, Boolean, Integer, Float
from sqlalchemy.dialects.postgresql import UUID, ARRAY

from .base import Base


class Job(Base):
    """Model for job listings"""
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Job identifiers
    external_id = Column(String, unique=True, nullable=False, index=True)
    source = Column(String, nullable=False, index=True)  # e.g., "linkedin", "indeed"
    url = Column(String, nullable=False)
    
    # Basic job info
    title = Column(String, nullable=False, index=True)
    company = Column(String, nullable=False, index=True)
    location = Column(String, nullable=True, index=True)
    
    # Detailed job info
    description = Column(Text, nullable=False)
    salary_range = Column(JSON, nullable=True)  # {min: float, max: float, currency: str}
    employment_type = Column(String, nullable=True)  # full-time, part-time, contract
    experience_level = Column(String, nullable=True)
    
    # Job requirements
    required_skills = Column(ARRAY(String), nullable=True)
    preferred_skills = Column(ARRAY(String), nullable=True)
    education = Column(String, nullable=True)
    
    # Metadata
    posted_at = Column(DateTime, nullable=True)
    scraped_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    raw_data = Column(JSON, nullable=True)  # Store original scraped data
    
    # Processing status
    is_processed = Column(Boolean, default=False, nullable=False)
    processing_status = Column(String, nullable=True)  # success, failed, pending
    processing_error = Column(Text, nullable=True)
    
    # Search optimization
    search_vector = Column(Text, nullable=True)  # For full-text search
    embedding = Column(ARRAY(Float), nullable=True)  # For similarity search
    
    # Matching scores
    relevance_score = Column(Integer, nullable=True)  # 0-100
    match_quality = Column(JSON, nullable=True)  # Detailed matching metrics
    
    def __repr__(self) -> str:
        return f"<Job {self.title} at {self.company}>" 