import requests
import json
import os
import sys
import time

# Base URL for API
BASE_URL = "http://localhost:8000/api/v1"

def test_embeddings_api():
    """Test the embeddings API endpoints"""
    print("\n=== Testing Embeddings API ===")
    
    # Test get embedding info
    print("\nGetting embedding info...")
    response = requests.get(f"{BASE_URL}/embeddings/info")
    
    if response.status_code == 404:
        print("No embeddings found (expected if no embeddings have been created yet)")
    elif response.status_code == 200:
        print("Found existing embedding:")
        print(json.dumps(response.json(), indent=2))
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
    
    # Test create embedding
    print("\nCreating embedding...")
    # Find a PDF file to use for testing
    test_file = None
    for root, dirs, files in os.walk("../data"):
        for file in files:
            if file.endswith(".pdf"):
                test_file = os.path.join(root, file)
                break
        if test_file:
            break
    
    if not test_file:
        print("No PDF file found for testing")
        return
    
    print(f"Using test file: {test_file}")
    
    with open(test_file, "rb") as f:
        files = {"files": (os.path.basename(test_file), f)}
        response = requests.post(
            f"{BASE_URL}/embeddings/create",
            files=files,
            data={"model": "text-embedding-ada-002"}
        )
    
    if response.status_code == 200:
        print("Successfully created embedding:")
        print(json.dumps(response.json(), indent=2))
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
    
    # Test get embedding info again
    print("\nGetting embedding info after creation...")
    response = requests.get(f"{BASE_URL}/embeddings/info")
    
    if response.status_code == 200:
        print("Found embedding:")
        print(json.dumps(response.json(), indent=2))
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
    
    # Test delete embedding
    print("\nDeleting embedding...")
    response = requests.delete(f"{BASE_URL}/embeddings/delete")
    
    if response.status_code == 200:
        print("Successfully deleted embedding")
    else:
        print(f"Error: {response.status_code}")
        print(response.text)

def test_rag_shortlist_api():
    """Test the RAG shortlist API endpoint"""
    print("\n=== Testing RAG Shortlist API ===")
    
    # First, make sure we have jobs
    print("\nGetting jobs...")
    response = requests.get(f"{BASE_URL}/jobs")
    
    if response.status_code != 200 or not response.json():
        print("No jobs found. Please import jobs first.")
        return
    
    print(f"Found {len(response.json())} jobs")
    
    # Check if we have embeddings
    print("\nChecking for embeddings...")
    response = requests.get(f"{BASE_URL}/embeddings/info")
    
    if response.status_code == 404:
        print("No embeddings found. Please create embeddings first.")
        return
    
    print("Found embeddings")
    
    # Test RAG shortlist
    print("\nTesting RAG shortlist with FAISS pre-selection...")
    response = requests.post(
        f"{BASE_URL}/jobs/rag-shortlist",
        json={
            "method": "faiss_preselect",
            "faiss_threshold": 0.3,
            "temperature": 0.7
        }
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"Successfully shortlisted {result.get('shortlisted_count', 0)} jobs")
        if result.get('shortlisted_jobs'):
            print("First few shortlisted jobs:")
            for job in result.get('shortlisted_jobs')[:3]:
                print(f"- {job.get('title')} ({job.get('company')}): Score {job.get('score')}")
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
    
    # Test RAG shortlist with full LLM
    print("\nTesting RAG shortlist with full LLM...")
    response = requests.post(
        f"{BASE_URL}/jobs/rag-shortlist",
        json={
            "method": "full_llm",
            "temperature": 0.7
        }
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"Successfully shortlisted {result.get('shortlisted_count', 0)} jobs")
        if result.get('shortlisted_jobs'):
            print("First few shortlisted jobs:")
            for job in result.get('shortlisted_jobs')[:3]:
                print(f"- {job.get('title')} ({job.get('company')}): Score {job.get('score')}")
    else:
        print(f"Error: {response.status_code}")
        print(response.text)

def test_evaluate_api():
    """Test the evaluate API endpoint"""
    print("\n=== Testing Evaluate API ===")
    
    # First, make sure we have shortlisted jobs
    print("\nGetting jobs...")
    response = requests.get(f"{BASE_URL}/jobs")
    
    if response.status_code != 200 or not response.json():
        print("No jobs found. Please import jobs first.")
        return
    
    # Get a few job IDs for testing
    job_ids = list(response.json().keys())[:5]
    print(f"Using {len(job_ids)} jobs for testing")
    
    # Test evaluate
    print("\nTesting evaluate...")
    response = requests.post(
        f"{BASE_URL}/jobs/evaluate",
        json={
            "job_ids": job_ids,
            "temperature": 0.7
        }
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"Successfully evaluated {result.get('evaluated_count', 0)} jobs")
        if result.get('evaluated_jobs'):
            print("Evaluation results:")
            for job in result.get('evaluated_jobs'):
                print(f"- {job.get('title')} ({job.get('company')})")
                print(f"  Fit: {job.get('fit_percentage')}%")
                print(f"  Pros: {job.get('pros')}")
                print(f"  Cons: {job.get('cons')}")
                print()
    else:
        print(f"Error: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    # Check if API is running
    try:
        response = requests.get(f"{BASE_URL}")
        if response.status_code != 200:
            print(f"API not running or not accessible at {BASE_URL}")
            sys.exit(1)
    except requests.exceptions.ConnectionError:
        print(f"API not running or not accessible at {BASE_URL}")
        print("Please start the API server first")
        sys.exit(1)
    
    # Run tests
    test_embeddings_api()
    time.sleep(1)  # Add a small delay between tests
    test_rag_shortlist_api()
    time.sleep(1)  # Add a small delay between tests
    test_evaluate_api()
