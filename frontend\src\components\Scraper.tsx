/**
 * Simplified <PERSON>raper component for JoMaDe application.
 * This replaces the complex FirecrawlSettings component with a simpler approach.
 */

'use client';

import React, { useState } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  Flex,
  useToast,
  Alert,
  AlertIcon,
  Spinner,
  Badge,
  HStack,
} from '@chakra-ui/react';
import { FiRefreshCw } from 'react-icons/fi';
import { simpleApi } from '../services/simpleApi';

interface ScraperProps {
  onScrapeComplete?: () => void;
}

const Scraper: React.FC<ScraperProps> = ({ onScrapeComplete }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [scrapeResult, setScrapeResult] = useState<any>(null);
  
  const toast = useToast();

  // Handle scraping job URLs
  const handleScrapeJobs = async () => {
    setIsLoading(true);
    setError(null);
    setScrapeResult(null);

    try {
      const result = await simpleApi.scrapeJobs();
      setScrapeResult(result);

      toast({
        title: 'Jobs Scraped',
        description: `Successfully scraped ${result.job_count} jobs from ${result.url_count} URLs`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Notify parent component if callback provided
      if (onScrapeComplete) {
        onScrapeComplete();
      }
    } catch (err) {
      console.error('Error scraping jobs:', err);
      setError(err instanceof Error ? err.message : 'Failed to scrape jobs');

      toast({
        title: 'Scrape Failed',
        description: err instanceof Error ? err.message : 'Failed to scrape jobs',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box borderWidth="1px" borderRadius="lg" p={4}>
      <VStack align="stretch" spacing={4}>
        <Text fontSize="lg" fontWeight="bold">Job Scraping</Text>
        
        <Text>
          Use this feature to scrape job listings from the URLs you've added. 
          This will create job entries that you can then match against your CV.
        </Text>

        <Button
          colorScheme="blue"
          onClick={handleScrapeJobs}
          isLoading={isLoading}
          leftIcon={<FiRefreshCw />}
          width="full"
        >
          Scrape Job URLs
        </Button>

        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}

        {isLoading && (
          <Flex justify="center" align="center" py={4}>
            <Spinner size="md" mr={2} />
            <Text>Scraping jobs...</Text>
          </Flex>
        )}

        {scrapeResult && (
          <Box borderWidth="1px" borderRadius="md" p={3}>
            <Text fontWeight="bold" mb={2}>Scrape Results</Text>
            <HStack spacing={4}>
              <Badge colorScheme="blue">{scrapeResult.job_count} Jobs</Badge>
              <Badge colorScheme="purple">{scrapeResult.url_count} URLs</Badge>
              <Badge colorScheme="green">Success</Badge>
            </HStack>
          </Box>
        )}

        <Alert status="info">
          <AlertIcon />
          <Box>
            <Text fontWeight="bold">Note:</Text>
            <Text>
              Job scraping uses the URLs you've added in the URL Management section.
              Make sure you have added some URLs before scraping.
            </Text>
          </Box>
        </Alert>
      </VStack>
    </Box>
  );
};

export default Scraper;
