'use client';

import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Divider,
  Text,
  useToast,
  SimpleGrid,
  Badge,
  IconButton,
  Flex,
  useColorModeValue,
  Select,
  Switch,
  FormHelperText,
} from '@chakra-ui/react';
import { FiUpload, FiFile, FiTrash2, FiSave, FiRefreshCw } from 'react-icons/fi';
import { Card } from '../../common';
import { apiService } from '../../../services';

const UserSettings: React.FC = () => {
  const [cvSummary, setCvSummary] = useState<string>('');
  const [isCVLoading, setIsCVLoading] = useState<boolean>(false);
  const [cvError, setCVError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [selectedModel, setSelectedModel] = useState<string>('text-embedding-ada-002');
  const [uploadedFiles, setUploadedFiles] = useState<{ name: string; size: string; date: string }[]>([
    { name: 'resume.pdf', size: '245 KB', date: '2025-05-10' },
    { name: 'cover_letter.docx', size: '125 KB', date: '2025-05-10' },
  ]);

  const toast = useToast();
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Fetch CV summary on component mount
  useEffect(() => {
    const fetchCVSummary = async () => {
      setIsCVLoading(true);
      setCVError(null);

      try {
        // Use a hardcoded value for now
        const hardcodedSummary = "Senior executive with over 20 years of experience in the manufacturing and industrial sectors, specializing in supply chain management, business development, and sales. Proven track record in strategic company development, turnaround management, and restructuring of international operations. Successfully led global sales teams and key account management, achieving a revenue increase from €23M in 2019 to a projected €45M in 2023, with a CAGR of 11.5%. Managed budgets of up to €40M and teams of 16 employees, ensuring operational stability and financial health during challenging market conditions. Experienced in restructuring foreign business units, acquisitions, and establishing new trading entities. Holds an MBA in General Management with a focus on financial management and a B.Sc. in Business Administration. Fluent in German, English, and Swedish. Proficient in SAP, CRM systems, and Lean manufacturing methodologies. Adept at project management, cost optimization, and team leadership.";

        setCvSummary(hardcodedSummary);

        // In a real implementation, we would use the API service
        // const data = await apiService.getCVSummary();
        // setCvSummary(data.summary || '');
      } catch (err) {
        console.error('Error fetching CV summary:', err);
        setCVError(err instanceof Error ? err.message : 'Failed to fetch CV summary');
        toast({
          title: 'Error',
          description: err instanceof Error ? err.message : 'Failed to fetch CV summary',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsCVLoading(false);
      }
    };

    fetchCVSummary();
  }, [toast]);

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    setIsUploading(true);

    // Simulate file upload
    setTimeout(() => {
      const file = e.target.files![0];
      const newFile = {
        name: file.name,
        size: `${Math.round(file.size / 1024)} KB`,
        date: new Date().toISOString().split('T')[0],
      };

      setUploadedFiles([...uploadedFiles, newFile]);
      setIsUploading(false);

      toast({
        title: 'File uploaded',
        description: `${file.name} has been uploaded successfully`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Clear the input
      e.target.value = '';
    }, 1500);
  };

  // Handle file deletion
  const handleDeleteFile = (index: number) => {
    const newFiles = [...uploadedFiles];
    const deletedFile = newFiles[index];
    newFiles.splice(index, 1);
    setUploadedFiles(newFiles);

    toast({
      title: 'File deleted',
      description: `${deletedFile.name} has been deleted`,
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  // Handle CV summary update
  const handleSaveCVSummary = async () => {
    setIsCVLoading(true);
    setCVError(null);

    try {
      // In a real implementation, we would call the API
      // await apiService.updateCVSummary(cvSummary);

      // For now, just show a success message
      toast({
        title: 'CV Summary saved',
        description: 'Your CV summary has been updated successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error saving CV summary:', err);
      setCVError(err instanceof Error ? err.message : 'Failed to save CV summary');
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to save CV summary',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsCVLoading(false);
    }
  };

  // Handle embedding creation
  const handleCreateEmbedding = () => {
    if (uploadedFiles.length === 0) {
      toast({
        title: 'No files to embed',
        description: 'Please upload at least one file',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsProcessing(true);

    // Simulate embedding creation
    setTimeout(() => {
      setIsProcessing(false);

      toast({
        title: 'Embedding created',
        description: `Created embeddings for ${uploadedFiles.length} files using ${selectedModel}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }, 2000);
  };

  return (
    <VStack spacing={6} align="stretch">
      {/* Document Upload Section */}
      <Card title="Document Management" subtitle="Upload and manage your CV and other documents">
        <VStack spacing={4} align="stretch">
          <FormControl>
            <FormLabel>Upload Documents</FormLabel>
            <Flex>
              <Input
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                onChange={handleFileUpload}
                display="none"
                id="file-upload"
              />
              <Button
                as="label"
                htmlFor="file-upload"
                leftIcon={<FiUpload />}
                colorScheme="blue"
                isLoading={isUploading}
                cursor="pointer"
              >
                Upload File
              </Button>
            </Flex>
            <FormHelperText>
              Supported formats: PDF, DOC, DOCX, TXT
            </FormHelperText>
          </FormControl>

          <Box
            borderWidth="1px"
            borderRadius="md"
            borderColor={borderColor}
            p={4}
            maxH="200px"
            overflowY="auto"
          >
            {uploadedFiles.length > 0 ? (
              <VStack spacing={2} align="stretch">
                {uploadedFiles.map((file, index) => (
                  <Flex
                    key={index}
                    justify="space-between"
                    align="center"
                    p={2}
                    borderRadius="md"
                    bg={useColorModeValue('gray.50', 'gray.700')}
                  >
                    <HStack>
                      <FiFile />
                      <Text fontWeight="medium">{file.name}</Text>
                      <Badge colorScheme="blue">{file.size}</Badge>
                      <Text fontSize="sm" color="gray.500">
                        {file.date}
                      </Text>
                    </HStack>
                    <IconButton
                      aria-label="Delete file"
                      icon={<FiTrash2 />}
                      size="sm"
                      variant="ghost"
                      colorScheme="red"
                      onClick={() => handleDeleteFile(index)}
                    />
                  </Flex>
                ))}
              </VStack>
            ) : (
              <Text color="gray.500" textAlign="center">
                No files uploaded yet
              </Text>
            )}
          </Box>
        </VStack>
      </Card>

      {/* Embedding Management Section */}
      <Card title="Embedding Management" subtitle="Create and manage embeddings for your documents">
        <VStack spacing={4} align="stretch">
          <FormControl>
            <FormLabel>Embedding Model</FormLabel>
            <Select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
            >
              <option value="text-embedding-ada-002">text-embedding-ada-002</option>
              <option value="text-embedding-3-small">text-embedding-3-small</option>
              <option value="text-embedding-3-large">text-embedding-3-large</option>
            </Select>
            <FormHelperText>
              Select the model to use for creating embeddings
            </FormHelperText>
          </FormControl>

          <Button
            leftIcon={<FiRefreshCw />}
            colorScheme="blue"
            onClick={handleCreateEmbedding}
            isLoading={isProcessing}
            isDisabled={uploadedFiles.length === 0}
          >
            Create Embeddings
          </Button>

          <Divider />

          <Box>
            <Text fontWeight="medium" mb={2}>
              Current Embedding Status
            </Text>
            <Badge colorScheme="green" mb={2}>
              Active
            </Badge>
            <SimpleGrid columns={2} spacing={4}>
              <Box>
                <Text fontSize="sm" color="gray.500">
                  Model
                </Text>
                <Text>text-embedding-ada-002</Text>
              </Box>
              <Box>
                <Text fontSize="sm" color="gray.500">
                  Created
                </Text>
                <Text>2025-05-10</Text>
              </Box>
              <Box>
                <Text fontSize="sm" color="gray.500">
                  Files
                </Text>
                <Text>2</Text>
              </Box>
              <Box>
                <Text fontSize="sm" color="gray.500">
                  Size
                </Text>
                <Text>1.2 MB</Text>
              </Box>
            </SimpleGrid>
          </Box>
        </VStack>
      </Card>

      {/* CV Summary Section */}
      <Card title="CV Summary" subtitle="Edit your CV summary for job matching">
        <VStack spacing={4} align="stretch">
          <FormControl isDisabled={isCVLoading}>
            <FormLabel>CV Summary</FormLabel>
            <Textarea
              value={cvSummary}
              onChange={(e) => setCvSummary(e.target.value)}
              placeholder="Enter your CV summary here..."
              minHeight="200px"
              resize="vertical"
              isDisabled={isCVLoading}
            />
            <FormHelperText>
              This summary will be used for matching jobs to your profile
            </FormHelperText>
          </FormControl>

          {cvError && (
            <Box>
              <Text color="red.500" fontSize="sm">
                Error: {cvError}
              </Text>
            </Box>
          )}

          <Button
            leftIcon={<FiSave />}
            colorScheme="blue"
            onClick={handleSaveCVSummary}
            alignSelf="flex-end"
            isLoading={isCVLoading}
            loadingText="Saving..."
          >
            Save Summary
          </Button>
        </VStack>
      </Card>
    </VStack>
  );
};

export default UserSettings;
