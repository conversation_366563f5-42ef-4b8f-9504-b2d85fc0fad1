'use client';

import React, { useState } from 'react';
import {
  Box,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Heading,
  Text,
  Flex,
  Container,
  useColorModeValue,
  VStack,
} from '@chakra-ui/react';
import JobUrls from '../../components/JobUrls';
import CV from '../../components/CV';
import Jobs from '../../components/Jobs';
import Scraper from '../../components/Scraper';

const SimplePage: React.FC = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Handle tab change
  const handleTabChange = (index: number) => {
    setTabIndex(index);
  };

  // Handle scrape complete
  const handleScrapeComplete = () => {
    // Switch to Jobs tab after scraping
    setTabIndex(2);
  };

  return (
    <Box minH="100vh" bg={bgColor} py={8}>
      <Container maxW="container.xl">
        <VStack spacing={6} align="stretch">
          {/* Header */}
          <Flex direction="column" mb={6}>
            <Heading as="h1" size="xl" mb={2}>JoMaDe</Heading>
            <Text color="gray.500">Job Market Detector - Simplified Version</Text>
          </Flex>

          {/* Main Content */}
          <Box
            bg={cardBg}
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
            overflow="hidden"
          >
            <Tabs
              variant="enclosed"
              colorScheme="blue"
              index={tabIndex}
              onChange={handleTabChange}
              isLazy
            >
              <TabList bg={useColorModeValue('gray.50', 'gray.700')} px={4} pt={2}>
                <Tab _selected={{ bg: cardBg, borderBottomColor: cardBg }}>URL Management</Tab>
                <Tab _selected={{ bg: cardBg, borderBottomColor: cardBg }}>CV</Tab>
                <Tab _selected={{ bg: cardBg, borderBottomColor: cardBg }}>Jobs</Tab>
                <Tab _selected={{ bg: cardBg, borderBottomColor: cardBg }}>Scraper</Tab>
              </TabList>

              <TabPanels>
                <TabPanel p={4}>
                  <JobUrls />
                </TabPanel>
                <TabPanel p={4}>
                  <CV />
                </TabPanel>
                <TabPanel p={4}>
                  <Jobs />
                </TabPanel>
                <TabPanel p={4}>
                  <Scraper onScrapeComplete={handleScrapeComplete} />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Box>

          {/* Footer */}
          <Box textAlign="center" mt={8} fontSize="sm" color="gray.500">
            <Text>JoMaDe - Simplified Version</Text>
            <Text>© {new Date().getFullYear()} JoMaDe</Text>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default SimplePage;
