'use client';

import React, { useState, useEffect } from 'react';
import { Box, Heading, Text, Button, VStack, Code, Alert, AlertIcon, Container, Divider } from '@chakra-ui/react';
import { PageHeader } from '@/components/common';

export default function DebugPage() {
  const [apiStatus, setApiStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [apiError, setApiError] = useState<string | null>(null);

  useEffect(() => {
    const checkApi = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/health`);
        const data = await response.json();
        setApiResponse(data);
        setApiStatus('success');
      } catch (error) {
        console.error('API check failed:', error);
        setApiError(error instanceof Error ? error.message : 'Unknown error');
        setApiStatus('error');
      }
    };

    checkApi();
  }, []);

  return (
    <Container maxW="container.lg" py={8}>
      <PageHeader 
        title="Debug Page" 
        subtitle="Testing basic rendering and API connectivity"
        breadcrumbs={[
          { label: 'Home', href: '/' },
          { label: 'Debug' }
        ]}
      />

      <VStack spacing={8} align="stretch" mt={8}>
        <Box p={6} borderRadius="lg" bg="white" boxShadow="sm">
          <Heading size="md" mb={4}>Frontend Status</Heading>
          <Alert status="success" mb={4}>
            <AlertIcon />
            Frontend is rendering correctly
          </Alert>
          <Text>Environment: {process.env.NODE_ENV}</Text>
          <Text>API URL: {process.env.NEXT_PUBLIC_API_URL || 'Not set'}</Text>
        </Box>

        <Box p={6} borderRadius="lg" bg="white" boxShadow="sm">
          <Heading size="md" mb={4}>API Connection</Heading>
          
          {apiStatus === 'loading' && (
            <Alert status="info">
              <AlertIcon />
              Checking API connection...
            </Alert>
          )}
          
          {apiStatus === 'success' && (
            <>
              <Alert status="success" mb={4}>
                <AlertIcon />
                Connected to API successfully
              </Alert>
              <Heading size="sm" mb={2}>Response:</Heading>
              <Code p={3} borderRadius="md" display="block" whiteSpace="pre-wrap">
                {JSON.stringify(apiResponse, null, 2)}
              </Code>
            </>
          )}
          
          {apiStatus === 'error' && (
            <>
              <Alert status="error" mb={4}>
                <AlertIcon />
                Failed to connect to API
              </Alert>
              <Text color="red.500">{apiError}</Text>
              <Text mt={2}>
                Make sure the backend server is running at {process.env.NEXT_PUBLIC_API_URL}
              </Text>
            </>
          )}
          
          <Button 
            mt={4} 
            colorScheme="blue" 
            onClick={() => {
              setApiStatus('loading');
              const checkApi = async () => {
                try {
                  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/health`);
                  const data = await response.json();
                  setApiResponse(data);
                  setApiStatus('success');
                } catch (error) {
                  console.error('API check failed:', error);
                  setApiError(error instanceof Error ? error.message : 'Unknown error');
                  setApiStatus('error');
                }
              };
              checkApi();
            }}
          >
            Retry API Check
          </Button>
        </Box>

        <Box p={6} borderRadius="lg" bg="white" boxShadow="sm">
          <Heading size="md" mb={4}>Component Test</Heading>
          <Text mb={4}>Testing basic Chakra UI components:</Text>
          
          <Divider my={4} />
          
          <VStack align="start" spacing={4}>
            <Button colorScheme="blue">Primary Button</Button>
            <Button colorScheme="gray">Secondary Button</Button>
            <Alert status="warning">
              <AlertIcon />
              This is a warning alert
            </Alert>
          </VStack>
        </Box>
      </VStack>
    </Container>
  );
}
