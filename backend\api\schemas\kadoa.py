from typing import List, Dict, Optional, Literal
from pydantic import BaseModel, HttpUrl, validator, <PERSON>
from datetime import datetime
import bleach

SchemaType = Literal["DETAIL", "LIST"]
WorkflowState = Literal["PENDING", "RUNNING", "COMPLETED", "FAILED", "PAUSED", "ACTIVE", "PREVIEW"]
EmploymentType = Literal["FULL_TIME", "PART_TIME", "CONTRACT", "TEMPORARY", "INTERNSHIP"]
ProcessingStatus = Literal["PENDING", "PROCESSING", "COMPLETED", "FAILED"]
SourceType = Literal["Kadoa", "Manual"]


class WorkflowBase(BaseModel):
    """Base Kadoa workflow schema"""
    url: HttpUrl
    schema_type: SchemaType = "DETAIL"

    @validator('url')
    def validate_url(cls, v):
        allowed_domains = ['linkedin.com', 'indeed.com', 'glassdoor.com']  # Add your allowed domains
        if not any(domain in str(v) for domain in allowed_domains):
            raise ValueError("URL domain not in allowed list")
        return v


class WorkflowCreate(WorkflowBase):
    """Schema for creating a new workflow"""
    pass


class WorkflowResponse(BaseModel):
    """Schema for workflow response"""
    id: str = Field(alias="_id")
    url: HttpUrl
    state: WorkflowState = Field(alias="displayState")
    created_at: datetime = Field(alias="createdAt")
    updated_at: datetime = Field(alias="updatedAt")
    name: Optional[str] = None
    schema_type: SchemaType = "DETAIL"

    @validator('name')
    def sanitize_name(cls, v):
        if v:
            return bleach.clean(v, strip=True)
        return v

    class Config:
        populate_by_name = True
        from_attributes = True


class JobBase(BaseModel):
    """Base schema for job data"""
    title: str
    company: str
    location: Optional[str] = None
    description: str
    salary_range: Optional[Dict] = None
    employment_type: Optional[EmploymentType] = None
    experience_level: Optional[str] = None
    required_skills: Optional[List[str]] = None
    preferred_skills: Optional[List[str]] = None
    education: Optional[str] = None
    link: Optional[HttpUrl] = None
    reference: Optional[str] = None
    remote_work: Optional[bool] = None

    @validator('title')
    def validate_title(cls, v):
        if not v or len(v) > 200:
            raise ValueError("Title must be between 1 and 200 characters")
        return v

    @validator('company')
    def validate_company(cls, v):
        if not v or len(v) > 200:
            raise ValueError("Company must be between 1 and 200 characters")
        return v

    @validator('description', 'location', 'education', 'reference')
    def sanitize_text(cls, v):
        if v:
            return bleach.clean(v, strip=True)
        return v

    @validator('required_skills', 'preferred_skills')
    def sanitize_skills(cls, v):
        if v:
            return [bleach.clean(skill, strip=True) for skill in v]
        return v

    @validator('salary_range')
    def validate_salary_range(cls, v):
        if v:
            if not isinstance(v, dict):
                raise ValueError("Salary range must be a dictionary")
            if 'min' in v and not isinstance(v['min'], (int, float)):
                raise ValueError("Minimum salary must be a number")
            if 'max' in v and not isinstance(v['max'], (int, float)):
                raise ValueError("Maximum salary must be a number")
            if 'currency' in v and not isinstance(v['currency'], str):
                raise ValueError("Currency must be a string")
        return v


class JobCreate(JobBase):
    """Schema for creating a new job"""
    workflow_id: str
    external_id: str
    source: SourceType = "Kadoa"
    raw_data: Optional[Dict] = None


class JobResponse(JobBase):
    """Schema for job response"""
    id: str
    workflow_id: str
    external_id: str
    source: str
    created_at: datetime
    updated_at: datetime
    scraped_at: datetime
    is_processed: bool
    processing_status: Optional[ProcessingStatus] = None
    processing_error: Optional[str] = None

    class Config:
        from_attributes = True


class WorkflowJobsResponse(BaseModel):
    """Schema for workflow jobs response"""
    workflow_id: str
    total_jobs: int
    jobs: List[JobResponse]

    class Config:
        from_attributes = True 