from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, DateTime
from datetime import datetime
import re

class CustomBase:
    # Generate __tablename__ automatically based on class name
    @declared_attr
    def __tablename__(cls):
        # Convert CamelCase to snake_case
        name = re.sub('(?<!^)(?=[A-Z])', '_', cls.__name__).lower()
        return name
    
    # Common columns for all models
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Create a base class for all SQLAlchemy models
Base = declarative_base(cls=CustomBase)
