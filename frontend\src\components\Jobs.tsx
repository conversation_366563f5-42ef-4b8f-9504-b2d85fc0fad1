/**
 * Simplified Jobs component for JoMaDe application.
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Flex,
  useToast,
  Alert,
  AlertIcon,
  Spinner,
  Badge,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Checkbox,
  Select,
  useColorModeValue,
  IconButton,
} from '@chakra-ui/react';
import { FiRefreshCw, FiExternalLink, FiStar, FiFilter } from 'react-icons/fi';
import { Job } from '../types';
import { simpleApi } from '../services/simpleApi';

const Jobs: React.FC = () => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedJobs, setSelectedJobs] = useState<string[]>([]);
  const [filter, setFilter] = useState<string>('all'); // 'all', 'shortlisted', or a source prefix
  
  const toast = useToast();
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Fetch jobs on component mount and when filter changes
  useEffect(() => {
    const fetchJobs = async () => {
      setIsLoading(true);
      setError(null);

      try {
        let fetchedJobs: Job[];
        
        if (filter === 'all') {
          fetchedJobs = await simpleApi.getJobs();
        } else if (filter === 'shortlisted') {
          fetchedJobs = await simpleApi.getJobs(undefined, true);
        } else {
          // Filter by source prefix
          fetchedJobs = await simpleApi.getJobs(filter);
        }
        
        setJobs(fetchedJobs);
      } catch (err) {
        console.error('Error fetching jobs:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch jobs');
        
        toast({
          title: 'Error',
          description: err instanceof Error ? err.message : 'Failed to fetch jobs',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchJobs();
  }, [filter, toast]);

  // Handle refreshing jobs
  const handleRefresh = async () => {
    setIsLoading(true);
    setError(null);

    try {
      let fetchedJobs: Job[];
      
      if (filter === 'all') {
        fetchedJobs = await simpleApi.getJobs();
      } else if (filter === 'shortlisted') {
        fetchedJobs = await simpleApi.getJobs(undefined, true);
      } else {
        // Filter by source prefix
        fetchedJobs = await simpleApi.getJobs(filter);
      }
      
      setJobs(fetchedJobs);
      
      toast({
        title: 'Jobs Refreshed',
        description: `Loaded ${fetchedJobs.length} jobs`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error refreshing jobs:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh jobs');
      
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to refresh jobs',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle toggling job shortlist status
  const handleToggleShortlist = async (jobId: string, currentStatus: boolean) => {
    try {
      await simpleApi.updateJob(jobId, { isShortlisted: !currentStatus });
      
      // Update local state
      setJobs(jobs.map(job => 
        job.id === jobId ? { ...job, isShortlisted: !currentStatus } : job
      ));
      
      toast({
        title: currentStatus ? 'Removed from Shortlist' : 'Added to Shortlist',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error updating job:', err);
      
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to update job',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Handle selecting all jobs
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedJobs(jobs.map(job => job.id));
    } else {
      setSelectedJobs([]);
    }
  };

  // Handle selecting a single job
  const handleSelectJob = (id: string, isChecked: boolean) => {
    if (isChecked) {
      setSelectedJobs([...selectedJobs, id]);
    } else {
      setSelectedJobs(selectedJobs.filter(jobId => jobId !== id));
    }
  };

  // Open job link in new tab
  const openJobLink = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <Box>
      <VStack spacing={4} align="stretch">
        {/* Controls */}
        <Flex justify="space-between" align="center" wrap="wrap" gap={2}>
          <HStack>
            <Text fontWeight="bold">Filter:</Text>
            <Select 
              size="sm" 
              value={filter} 
              onChange={(e) => setFilter(e.target.value)}
              width="auto"
            >
              <option value="all">All Jobs</option>
              <option value="shortlisted">Shortlisted</option>
              <option value="AAA">Source: AAA</option>
              <option value="AAB">Source: AAB</option>
              <option value="AAC">Source: AAC</option>
            </Select>
          </HStack>
          
          <Button
            leftIcon={<FiRefreshCw />}
            colorScheme="blue"
            size="sm"
            isLoading={isLoading}
            onClick={handleRefresh}
          >
            Refresh
          </Button>
        </Flex>

        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}

        {/* Jobs Table */}
        <Box borderWidth="1px" borderRadius="lg" borderColor={borderColor} overflow="hidden">
          {isLoading ? (
            <Flex justify="center" align="center" p={4}>
              <Spinner size="md" color="blue.500" />
            </Flex>
          ) : (
            <Table variant="simple" size="sm">
              <Thead>
                <Tr>
                  <Th width="20px" px={1} py={1}>
                    <Checkbox
                      size="sm"
                      isChecked={
                        selectedJobs.length === jobs.length && jobs.length > 0
                      }
                      isIndeterminate={
                        selectedJobs.length > 0 && selectedJobs.length < jobs.length
                      }
                      onChange={handleSelectAll}
                    />
                  </Th>
                  <Th width="60px" px={1} py={1}>ID</Th>
                  <Th px={1} py={1}>Title</Th>
                  <Th width="120px" px={1} py={1}>Company</Th>
                  <Th width="100px" px={1} py={1}>Location</Th>
                  <Th width="80px" px={1} py={1}>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {jobs.length === 0 ? (
                  <Tr>
                    <Td colSpan={6} textAlign="center" py={2}>
                      <Text fontSize="sm">No jobs found. Try scraping some jobs first.</Text>
                    </Td>
                  </Tr>
                ) : (
                  jobs.map((job) => (
                    <Tr key={job.id}>
                      <Td px={1} py={1}>
                        <Checkbox
                          size="sm"
                          isChecked={selectedJobs.includes(job.id)}
                          onChange={(e) => handleSelectJob(job.id, e.target.checked)}
                        />
                      </Td>
                      <Td px={1} py={1} fontSize="xs">
                        <Badge colorScheme="blue">{job.id}</Badge>
                      </Td>
                      <Td px={1} py={1} fontSize="sm" fontWeight={job.isShortlisted ? "bold" : "normal"}>
                        <Flex align="center">
                          {job.isShortlisted && (
                            <Box color="yellow.500" mr={1}>
                              <FiStar />
                            </Box>
                          )}
                          <Text noOfLines={1}>{job.title}</Text>
                        </Flex>
                      </Td>
                      <Td px={1} py={1} fontSize="xs">
                        {job.company || 'Unknown'}
                      </Td>
                      <Td px={1} py={1} fontSize="xs">
                        {job.location || 'Remote'}
                      </Td>
                      <Td px={1} py={1}>
                        <HStack spacing={1}>
                          <IconButton
                            aria-label={job.isShortlisted ? "Remove from shortlist" : "Add to shortlist"}
                            icon={<FiStar />}
                            size="xs"
                            variant="ghost"
                            colorScheme={job.isShortlisted ? "yellow" : "gray"}
                            onClick={() => handleToggleShortlist(job.id, !!job.isShortlisted)}
                          />
                          {job.link && (
                            <IconButton
                              aria-label="Open job link"
                              icon={<FiExternalLink />}
                              size="xs"
                              variant="ghost"
                              onClick={() => openJobLink(job.link!)}
                            />
                          )}
                        </HStack>
                      </Td>
                    </Tr>
                  ))
                )}
              </Tbody>
            </Table>
          )}
        </Box>
      </VStack>
    </Box>
  );
};

export default Jobs;
