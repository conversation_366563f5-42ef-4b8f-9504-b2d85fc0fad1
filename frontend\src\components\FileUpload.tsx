'use client';

import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  Heading,
  Flex,
  List,
  ListItem,
  IconButton,
  HStack,
  useToast,
  Alert,
  AlertIcon,
  Input,
} from '@chakra-ui/react';
import { FiTrash2 as DeleteIcon, <PERSON><PERSON><PERSON>clip as AttachmentIcon } from 'react-icons/fi';

interface FileUploadProps {
  onFilesUploaded?: (files: File[]) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFilesUploaded }) => {
  const [files, setFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setFiles(prevFiles => [...prevFiles, ...newFiles]);

      if (onFilesUploaded) {
        onFilesUploaded([...files, ...newFiles]);
      }

      toast({
        title: 'Files Added',
        description: `Added ${newFiles.length} file(s)`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Handle file deletion
  const handleDeleteFile = (index: number) => {
    setFiles(prevFiles => {
      const updatedFiles = [...prevFiles];
      updatedFiles.splice(index, 1);

      if (onFilesUploaded) {
        onFilesUploaded(updatedFiles);
      }

      return updatedFiles;
    });
  };

  // Handle file upload to server
  const handleUpload = async () => {
    if (files.length === 0) {
      setError('No files selected');
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      const response = await fetch('http://localhost:8000/api/v1/documents/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to upload files: ${response.status}`);
      }

      const result = await response.json();

      toast({
        title: 'Upload Complete',
        description: `Successfully uploaded ${files.length} file(s)`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Clear files after successful upload
      setFiles([]);

      if (onFilesUploaded) {
        onFilesUploaded([]);
      }
    } catch (err) {
      console.error('Error uploading files:', err);
      setError(err instanceof Error ? err.message : 'Failed to upload files');

      toast({
        title: 'Upload Failed',
        description: err instanceof Error ? err.message : 'Failed to upload files',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Trigger file input click
  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="white">
      <VStack align="stretch" spacing={4}>
        <Heading size="md">Upload CV & Documents</Heading>

        <Input
          type="file"
          multiple
          onChange={handleFileSelect}
          hidden
          ref={fileInputRef}
          accept=".pdf,.docx,.doc,.txt"
        />

        <HStack>
          <Button onClick={handleBrowseClick} leftIcon={<AttachmentIcon />} flex={1}>
            Browse Files
          </Button>

          <Button
            colorScheme="blue"
            onClick={handleUpload}
            isLoading={isUploading}
            isDisabled={files.length === 0}
            flex={1}
          >
            Upload Files
          </Button>
        </HStack>

        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}

        {files.length > 0 ? (
          <Box borderWidth="1px" borderRadius="md" p={2} maxHeight="200px" overflowY="auto">
            <List spacing={1}>
              {files.map((file, index) => (
                <ListItem key={index}>
                  <Flex justify="space-between" align="center">
                    <Text fontSize="sm" isTruncated maxWidth="80%">
                      {file.name}
                    </Text>
                    <IconButton
                      aria-label="Delete file"
                      icon={<DeleteIcon />}
                      size="sm"
                      colorScheme="red"
                      variant="ghost"
                      onClick={() => handleDeleteFile(index)}
                    />
                  </Flex>
                </ListItem>
              ))}
            </List>
          </Box>
        ) : (
          <Text color="gray.500">No files selected</Text>
        )}
      </VStack>
    </Box>
  );
};

export default FileUpload;
