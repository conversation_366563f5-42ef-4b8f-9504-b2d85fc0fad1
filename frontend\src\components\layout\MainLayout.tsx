'use client';

import React, { ReactNode, useState } from 'react';
import {
  Box,
  Flex,
  VStack,
  HStack,
  Text,
  IconButton,
  useColorModeValue,
  useDisclosure,
  Drawer,
  DrawerOverlay,
  DrawerContent,
  DrawerCloseButton,
  DrawerHeader,
  DrawerBody,
  Heading,
  Container,
  Spacer,
  Avatar,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Divider,
  useBreakpointValue,
} from '@chakra-ui/react';
import {
  FiMenu,
  FiHome,
  FiBriefcase,
  FiPieChart,
  FiSettings,
  FiUser,
  FiChevronDown,
} from 'react-icons/fi';
import Sidebar from './Sidebar';
import Header from './Header';
import Footer from './Footer';

interface MainLayoutProps {
  children: ReactNode;
  title?: string;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children, title = 'JoMaDe' }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const isMobile = useBreakpointValue({ base: true, md: false });

  // Background colors
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const contentBgColor = useColorModeValue('white', 'gray.800');

  return (
    <Box minH="100vh" bg={bgColor}>
      {/* Header */}
      <Header onMenuClick={onOpen} title={title} />

      {/* Main Content */}
      <Flex>
        {/* Sidebar - visible on desktop, hidden on mobile */}
        {!isMobile && <Sidebar />}

        {/* Mobile Sidebar Drawer */}
        <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
          <DrawerOverlay />
          <DrawerContent>
            <DrawerCloseButton />
            <DrawerHeader borderBottomWidth="1px">JoMaDe</DrawerHeader>
            <DrawerBody p={0}>
              <Sidebar isMobile onClose={onClose} />
            </DrawerBody>
          </DrawerContent>
        </Drawer>

        {/* Page Content */}
        <Box
          flex="1"
          p={{ base: 4, md: 6 }}
          ml={{ base: 0, md: '250px' }}
          transition="margin-left 0.3s"
        >
          <Container maxW="container.xl" p={0}>
            {children}
          </Container>

          {/* Footer */}
          <Footer />
        </Box>
      </Flex>
    </Box>
  );
};

export default MainLayout;
