import pandas as pd
import openai
import json
from typing import List, Dict
import time
from pathlib import Path

def evaluate_job_match(jobs_dict: Dict, temperature: float = 0.7) -> List[Dict]:
    """Evaluate shortlisted jobs using LLM with RAG context."""
    try:
        if not isinstance(jobs_dict, dict):
            print(f"❌ Error: Expected dictionary, got {type(jobs_dict)}")
            return None
            
        if not jobs_dict:
            print("⚠ No jobs to evaluate")
            return None
            
        print(f"\nEvaluating {len(jobs_dict)} jobs...")
        
        # Load FAISS index to verify we have embeddings
        embed_dir = Path(__file__).parent.parent / "data" / "embeddings"
        faiss_files = list(embed_dir.glob("*.faiss"))
        
        if not faiss_files:
            print("❌ No embeddings found. Please create embeddings first.")
            return None
        
        system_prompt = """You are a professional career advisor with access to the candidate's profile through RAG. 
        For each job, evaluate the fit based on the candidate's experience and qualifications.
        
        Return a JSON array with evaluations like this:
        [
            {
                "prefixindex": "A1",
                "fit_percentage": 85,
                "pros": "Strong leadership match, industry experience",
                "cons": "Location might require relocation"
            }
        ]

        IMPORTANT: 
        - Each object MUST have exactly these fields: prefixindex, fit_percentage, pros, cons
        - Use the exact job ID from the input (e.g., "A1")
        - fit_percentage must be a number 0-100 (no % symbol)
        - Return valid JSON array only, no other text
        - Base your evaluation on the candidate's actual experience from RAG"""
        
        # Process jobs in batches of 5
        batch_size = 5
        job_items = list(jobs_dict.items())
        job_batches = [job_items[i:i+batch_size] for i in range(0, len(job_items), batch_size)]
        
        results = []
        for batch_num, batch in enumerate(job_batches, 1):
            print(f"\n--- Processing Batch {batch_num}/{len(job_batches)} ---")
            
            # Include job descriptions for better evaluation
            batch_text = "\n\n".join([
                f"Job {job_id}:\n{job_data['jobtitle']}\n{job_data.get('job_description', '')}"
                for job_id, job_data in batch
            ])
            
            try:
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": f"Return a JSON array evaluating these jobs:\n\n{batch_text}"}
                    ],
                    temperature=temperature
                )
                
                # Get and print raw response for debugging
                response_text = response.choices[0].message.content.strip()
                print(f"\nRaw LLM Response:\n{response_text}\n")
                
                try:
                    evaluations = json.loads(response_text)
                    if not isinstance(evaluations, list):
                        print("❌ Response is not a JSON array")
                        return None
                    
                    for eval_item in evaluations:
                        if all(k in eval_item for k in ['prefixindex', 'fit_percentage', 'pros', 'cons']):
                            results.append(eval_item)
                            print(f"✅ Evaluated {eval_item['prefixindex']}: {eval_item['fit_percentage']}% match")
                        else:
                            print(f"❌ Missing required fields in: {eval_item}")
                            return None
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON parsing error: {str(e)}")
                    print(f"Invalid JSON response: {response_text}")
                    return None
                
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Error processing batch: {str(e)}")
                return None
        
        return results if results else None
        
    except Exception as e:
        print(f"❌ Error in evaluate_job_match: {str(e)}")
        return None

def format_evaluation_results(evaluation_results: List[Dict], shortlisted_jobs: List[tuple], threshold: int = 70) -> str:
    """
    Format evaluation results into HTML, showing both current and previous scores
    
    Args:
        evaluation_results: List of evaluation result dictionaries
        shortlisted_jobs: List of tuples (job_id, percentage) from shortlisting
        threshold: Minimum percentage for acceptance
        
    Returns:
        HTML formatted string of results
    """
    # Create dict of shortlisted jobs for easy lookup
    shortlist_scores = {job_id: score for job_id, score in shortlisted_jobs}
    
    html_parts = []
    for result in evaluation_results:
        job_id = result['job_id']
        current_score = result.get('match_percentage', 0)
        previous_score = shortlist_scores.get(job_id, 0)
        
        # Determine styling based on scores
        if current_score >= threshold:
            color = "green"
        else:
            color = "red"
            
        # Format the result with both scores
        html_parts.append(
            f'<div style="color: {color};">'
            f'<b>{result["job_title"]}</b><br>'
            f'Current Match: {current_score}% (Previous: {previous_score}%)<br>'
            f'{result["explanation"]}<br><br>'
            f'</div>'
        )
    
    return "\n".join(html_parts)
