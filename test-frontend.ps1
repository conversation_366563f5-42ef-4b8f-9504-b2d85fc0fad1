# Test Frontend Server
# This script starts only the frontend server for testing

# Define paths
$rootDir = Get-Location
$frontendDir = Join-Path -Path $rootDir -ChildPath 'frontend'

Write-Host '===== Starting Frontend Server for Testing =====' -ForegroundColor Cyan
Write-Host ''

# Check if npm is installed
try {
    $npmVersion = npm --version
    Write-Host "Found npm version: $npmVersion" -ForegroundColor Green
}
catch {
    Write-Host 'Error: npm not found. Please install Node.js and npm.' -ForegroundColor Red
    exit 1
}

# Start frontend server with more detailed logging
try {
    Write-Host 'Starting Next.js frontend server...' -ForegroundColor Yellow
    Write-Host "Working directory: $frontendDir" -ForegroundColor Yellow
    Write-Host "Command: npx next dev" -ForegroundColor Yellow

    # Check if .env.local exists and display its contents
    $envPath = Join-Path -Path $frontendDir -ChildPath '.env.local'
    if (Test-Path $envPath) {
        Write-Host "Found .env.local file:" -ForegroundColor Green
        Get-Content $envPath | ForEach-Object { Write-Host "  $_" -ForegroundColor Cyan }
    } else {
        Write-Host "Warning: .env.local file not found" -ForegroundColor Yellow
    }

    # Run the command directly in the current window
    Push-Location $frontendDir
    npx next dev
    Pop-Location
}
catch {
    Write-Host "Error starting frontend server: $_" -ForegroundColor Red
    exit 1
}
