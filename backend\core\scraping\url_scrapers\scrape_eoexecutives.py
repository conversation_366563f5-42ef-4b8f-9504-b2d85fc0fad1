import pandas as pd

def scrape(file_path):
    """
    Loads job titles from the Excel file.
    Stops at first NaN title.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        list: List of dictionaries containing job titles
    """
    try:
        print("Loading jobs from Excel...")
        # Read the Excel file
        df = pd.read_excel(file_path, sheet_name="Sheet1")
        
        # Debug: Print columns
        print("Columns in Excel:", df.columns)
        
        # Initialize list to store jobs
        jobs = []
        
        # Process each row until first NaN title
        for idx, row in df.iterrows():
            # Check for NaN title
            if pd.isna(row["Titel"]):
                print(f"Found empty title at row {idx+1}, stopping processing")
                break
                
            job = {
                "job_title": str(row["Titel"]).strip()
            }
            jobs.append(job)
            
        print(f"Successfully processed {len(jobs)} job titles")
        return jobs
        
    except Exception as e:
        print(f"Error in scrape function: {e}")
        return []

# Test the function
if __name__ == "__main__":
    test_file = "C:/Users/<USER>/Documents/VSCode/JobScraper/scraped_urls/careers_eoexecutives.xlsx"
    jobs = scrape(test_file)
    
    print("\nJobs found:")
    for i, job in enumerate(jobs, 1):
        print(f"A{i}: {job['job_title']}")