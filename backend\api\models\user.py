from datetime import datetime
from typing import Optional
from uuid import uuid4

from sqlalchemy import Column, String, DateTime, Text, JSON, Boolean, Integer
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship

from .base import Base


class User(Base):
    """Model for user profiles"""
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Authentication
    email = Column(String, unique=True, nullable=False, index=True)
    hashed_password = Column(String, nullable=True)  # Null for OAuth users
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # Profile
    full_name = Column(String, nullable=True)
    preferred_name = Column(String, nullable=True)
    profile_picture = Column(String, nullable=True)
    
    # Professional Info
    title = Column(String, nullable=True)
    skills = Column(ARRAY(String), nullable=True)
    experience = Column(JSON, nullable=True)  # List of work experiences
    education = Column(JSON, nullable=True)  # List of education history
    
    # Preferences
    job_preferences = Column(JSON, nullable=True)  # Desired job types, locations, etc.
    search_preferences = Column(JSON, nullable=True)  # Default search parameters
    notification_preferences = Column(JSON, nullable=True)
    
    # Job Search
    saved_searches = Column(JSON, nullable=True)  # List of saved search criteria
    saved_jobs = Column(JSON, nullable=True)  # List of saved job IDs
    applied_jobs = Column(JSON, nullable=True)  # List of jobs applied to
    
    # OAuth Info
    oauth_provider = Column(String, nullable=True)  # google, github, etc.
    oauth_id = Column(String, nullable=True)
    
    # System
    last_login = Column(DateTime, nullable=True)
    last_search = Column(DateTime, nullable=True)
    
    def __repr__(self) -> str:
        return f"<User {self.email}>" 