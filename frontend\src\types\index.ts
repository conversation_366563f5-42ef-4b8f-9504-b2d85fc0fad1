// Job Types
export interface Job {
  id: string;
  title: string;
  company?: string;
  location?: string;
  description?: string;
  isShortlisted?: boolean;
  score?: number;
  salary_range?: {
    min?: number;
    max?: number;
    currency?: string;
  };
  employment_type?: string;
  experience_level?: string;
  required_skills?: string[];
  preferred_skills?: string[];
  education?: string;
  link?: string;
  reference?: string;
  remote_work?: boolean;
  workflow_id?: string;
  external_id?: string;
  source?: string;
  created_at?: string;
  updated_at?: string;
}

// CV Types
export interface CVSummary {
  summary: string;
  skills: string[];
  experience: string[];
  education: string[];
}

// Document Types
export interface Document {
  id: string;
  filename: string;
  file_type: string;
  file_size: number;
  upload_date: string;
  is_processed: boolean;
}

// Embedding Types
export interface EmbeddingInfo {
  date: string;
  model: string;
  user: string;
  file_count: number;
  filename?: string;
}

// Workflow Types
export interface Workflow {
  id: string;
  url: string;
  schema_type: string;
  name?: string;
  state: string;
  created_at: string;
  updated_at: string;
  job_count?: number;
}

// Evaluation Types
export interface EvaluationResult {
  id: string;
  title: string;
  score: number;
  shortlistScore?: number;
  pros: string;
  cons: string;
  color: 'green' | 'orange' | 'red';
}

// URL Management Types
export interface JobSource {
  id: string;
  url: string;
  name?: string;
  is_active: boolean;
  last_scraped?: string;
  job_count?: number;
  created_at: string;
  updated_at?: string;
  prefix?: string; // Three-letter prefix (AAA, AAB, AAC, etc.)
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Settings Types
export interface SystemSettings {
  api_keys: {
    openai?: string;
    kadoa?: string;
    firecrawl?: string;
  };
  default_model: string;
  default_temperature: number;
  default_threshold: number;
}

export interface UserSettings {
  name?: string;
  email?: string;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    notifications_enabled: boolean;
  };
}
