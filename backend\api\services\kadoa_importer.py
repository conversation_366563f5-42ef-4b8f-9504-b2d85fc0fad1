from typing import List, Dict, Optional
import logging
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import HTTPException

from ..core.config import settings
from ..models.kadoa import KadoaWorkflow, KadoaJob
from .kadoa import KadoaService

logger = logging.getLogger(__name__)


class KadoaImporter:
    def __init__(self, db: Session):
        self.db = db
        self.kadoa_service = KadoaService()

    async def sync_workflows(self) -> List[KadoaWorkflow]:
        """Sync workflows from Kadoa API to database"""
        try:
            logger.info("Starting workflow sync from Kadoa")
            
            # Get workflows from Kadoa API
            api_workflows = await self.kadoa_service.list_workflows()
            logger.info(f"Retrieved {len(api_workflows)} workflows from Kadoa API")
            
            # Update or create workflows in database
            db_workflows = []
            for workflow in api_workflows:
                db_workflow = self.db.query(KadoaWorkflow).filter(
                    KadoaWorkflow.id == workflow.id
                ).first()
                
                if not db_workflow:
                    db_workflow = KadoaWorkflow(
                        id=workflow.id,
                        url=str(workflow.url),
                        state=workflow.state,
                        name=workflow.name,
                        schema_type=workflow.schema_type,
                        created_at=workflow.created_at,
                        updated_at=workflow.updated_at
                    )
                    self.db.add(db_workflow)
                else:
                    db_workflow.state = workflow.state
                    db_workflow.name = workflow.name
                    db_workflow.updated_at = workflow.updated_at
                
                db_workflows.append(db_workflow)
            
            self.db.commit()
            logger.info(f"Successfully synced {len(db_workflows)} workflows")
            return db_workflows
            
        except Exception as e:
            logger.error(f"Error syncing workflows: {str(e)}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Error syncing workflows: {str(e)}")

    async def sync_workflow_jobs(self, workflow_id: str) -> List[KadoaJob]:
        """Sync jobs for a specific workflow"""
        try:
            logger.info(f"Starting job sync for workflow {workflow_id}")
            
            # Get workflow from database
            workflow = self.db.query(KadoaWorkflow).filter(
                KadoaWorkflow.id == workflow_id
            ).first()
            
            if not workflow:
                raise HTTPException(status_code=404, detail="Workflow not found")
            
            # Get jobs from Kadoa API
            jobs = await self.kadoa_service.get_workflow_jobs(workflow_id)
            logger.info(f"Retrieved {len(jobs)} jobs from Kadoa API")
            
            # Update or create jobs in database
            db_jobs = []
            for job in jobs:
                db_job = self.db.query(KadoaJob).filter(
                    KadoaJob.workflow_id == workflow_id,
                    KadoaJob.external_id == job.external_id
                ).first()
                
                if not db_job:
                    # Create display text
                    display_parts = []
                    if job.title:
                        display_parts.append(job.title)
                    if job.company:
                        display_parts.append(job.company)
                    if job.location:
                        display_parts.append(job.location)
                    
                    display_text = ", ".join(filter(None, display_parts))
                    
                    db_job = KadoaJob(
                        id=f"{workflow_id}_{job.external_id}",
                        workflow_id=workflow_id,
                        external_id=job.external_id,
                        title=job.title,
                        company=job.company,
                        location=job.location,
                        description=job.description,
                        salary_range=job.salary_range,
                        employment_type=job.employment_type,
                        experience_level=job.experience_level,
                        required_skills=job.required_skills,
                        preferred_skills=job.preferred_skills,
                        education=job.education,
                        link=str(job.link) if job.link else None,
                        reference=job.reference,
                        remote_work=job.remote_work,
                        raw_data=job.dict(),
                        scraped_at=datetime.utcnow(),
                        display_text=display_text
                    )
                    self.db.add(db_job)
                else:
                    # Update existing job if needed
                    db_job.title = job.title
                    db_job.company = job.company
                    db_job.location = job.location
                    db_job.description = job.description
                    db_job.salary_range = job.salary_range
                    db_job.employment_type = job.employment_type
                    db_job.experience_level = job.experience_level
                    db_job.required_skills = job.required_skills
                    db_job.preferred_skills = job.preferred_skills
                    db_job.education = job.education
                    db_job.link = str(job.link) if job.link else None
                    db_job.reference = job.reference
                    db_job.remote_work = job.remote_work
                    db_job.raw_data = job.dict()
                    db_job.updated_at = datetime.utcnow()
                
                db_jobs.append(db_job)
            
            # Update workflow
            workflow.job_count = len(db_jobs)
            workflow.last_sync_at = datetime.utcnow()
            
            self.db.commit()
            logger.info(f"Successfully synced {len(db_jobs)} jobs for workflow {workflow_id}")
            return db_jobs
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error syncing jobs for workflow {workflow_id}: {str(e)}")
            self.db.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"Error syncing jobs for workflow {workflow_id}: {str(e)}"
            ) 