#Requires -Version 5.1
<#
.SYNOPSIS
    Starts the JoMaDe application (FastAPI backend and Next.js frontend).
.DESCRIPTION
    This script performs the following actions:
    1. Sets up the environment by defining paths.
    2. Checks for and activates the Python virtual environment for the backend.
    3. Installs backend Python dependencies from requirements.txt if needed.
    4. Starts the FastAPI backend server (Uvicorn) in a new console window.
    5. Installs frontend Node.js dependencies using npm if needed.
    6. Starts the Next.js frontend development server in a new console window.
    7. Opens the application in the default web browser.
    8. Waits for the user to press Enter to stop all servers.
.PARAMETER ForceInstallBackendDeps
    Forces reinstallation of backend Python dependencies.
.PARAMETER ForceInstallFrontendDeps
    Forces reinstallation of frontend Node.js dependencies.
.EXAMPLE
    .\run.ps1
    Starts the application with default behavior.
.EXAMPLE
    .\run.ps1 -ForceInstallBackendDeps -ForceInstallFrontendDeps
    Starts the application and forces reinstallation of all dependencies.
#>
[CmdletBinding()]
param (
    [switch]$ForceInstallBackendDeps,
    [switch]$ForceInstallFrontendDeps
)

Clear-Host

Write-Host "Starting JoMaDe Application..." -ForegroundColor Cyan

# --- Configuration ---
$projectRoot = $PSScriptRoot
$backendDir = Join-Path $projectRoot "backend"
$frontendDir = Join-Path $projectRoot "frontend"
$venvName = ".venv"
$venvPath = Join-Path $backendDir $venvName
$requirementsFile = Join-Path $backendDir "requirements.txt"
$envFile = Join-Path $projectRoot ".env"

# --- Environment Check ---
Write-Host "`n--- Environment Check ---" -ForegroundColor Magenta
if (-not (Test-Path $envFile)) {
    Write-Warning ".env file not found at '$envFile'. Some features may not work properly."
    Write-Host "Creating a basic .env file with default values..." -ForegroundColor Yellow
    $defaultEnvContent = @"
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/jomade_db

# API Keys (replace with your actual keys)
OPENAI_API_KEY=your_openai_api_key_here
FIRECRAWL_API_KEY=your_firecrawl_api_key_here

# Application Settings
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Environment
ENVIRONMENT=development
"@
    Set-Content -Path $envFile -Value $defaultEnvContent -Encoding UTF8
    Write-Host "Basic .env file created. Please update it with your actual API keys and configuration." -ForegroundColor Green
} else {
    Write-Host ".env file found." -ForegroundColor Green
}

# --- Helper Functions ---

# Function to check if a port is in use
function Test-PortInUse {
    param ([int]$Port)
    try {
        $tcpConnection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        if ($tcpConnection) {
            return $true
        }
        return $false
    }
    catch {
        Write-Warning "Error checking port ${Port}: $($_.Exception.Message)"
        return $false
    }
}

# Function to stop a process using a specific port
function Stop-ProcessUsingPort {
    param ([int]$Port)
    $processId = $null
    try {
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($connection) {
            $processId = $connection.OwningProcess
            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "Stopping process '$($process.ProcessName)' (PID: $processId) using port $Port..." -ForegroundColor Yellow
                Stop-Process -Id $processId -Force -ErrorAction Stop
                Write-Host "Process (PID: $processId) stopped." -ForegroundColor Green
                return $true
            } else {
                Write-Warning "Could not find process with PID $processId that was using port $Port."
                return $false
            }
        } else {
            Write-Host "No process found using port $Port." -ForegroundColor DarkGray
            return $true
        }
    }
    catch {
        Write-Warning "Error stopping process (PID: $processId) on port ${Port}: $($_.Exception.Message)"
        return $false
    }
}

# Function to find an executable in PATH or common locations
function Find-Executable {
    param (
        [string]$CommandName,
        [string[]]$AdditionalPaths = @()
    )
    $executable = Get-Command $CommandName -ErrorAction SilentlyContinue
    if ($executable) {
        return $executable.Source
    }

    # Check common paths if not found in PATH
    $commonPaths = @(
        "$env:ProgramFiles\nodejs",
        "$env:ProgramFiles(x86)\nodejs",
        "$env:LOCALAPPDATA\Programs\Python\Python*\Scripts",
        "$env:ProgramFiles\Python*\Scripts"
    ) + $AdditionalPaths

    foreach ($path in $commonPaths) {
        $expandedPath = $ExecutionContext.SessionState.Path.GetUnresolvedProviderPathFromPSPath($path)
        if (Test-Path (Join-Path $expandedPath "$CommandName.exe")) {
            return (Join-Path $expandedPath "$CommandName.exe")
        }
        if (Test-Path (Join-Path $expandedPath $CommandName)) {
            return (Join-Path $expandedPath $CommandName)
        }
    }
    return $null
}

# --- Backend Setup ---
Write-Host "`n--- Backend Setup ---" -ForegroundColor Magenta

# 1. Python Virtual Environment
$venvPythonPath = Join-Path (Join-Path $venvPath "Scripts") "python.exe"
$venvPipPath = Join-Path (Join-Path $venvPath "Scripts") "pip.exe"

if (-not (Test-Path $venvPath)) {
    Write-Host "Python virtual environment not found at '$venvPath'. Creating..." -ForegroundColor Yellow
    $pythonExecutable = Find-Executable -CommandName "python"
    if (-not $pythonExecutable) {
        Write-Error "Python executable not found. Please ensure Python 3.7+ is installed and in your PATH."
        exit 1
    }
    Write-Host "Using Python: $pythonExecutable"
    Push-Location $backendDir
    try {
        & $pythonExecutable -m venv $venvName
        Write-Host "Virtual environment created." -ForegroundColor Green
        $ForceInstallBackendDeps = $true
    }
    catch {
        Write-Error "Failed to create virtual environment: $($_.Exception.Message)"
        exit 1
    }
    finally {
        Pop-Location
    }
} else {
    Write-Host "Python virtual environment found at '$venvPath'." -ForegroundColor Green
}

# 2. Install/Update Backend Dependencies
$needsBackendInstall = $ForceInstallBackendDeps -or (-not (Test-Path (Join-Path $venvPath "pyvenv.cfg"))) -or (-not (Test-Path (Join-Path (Join-Path $venvPath "Lib") (Join-Path "site-packages" "fastapi"))))

if ($needsBackendInstall) {
    Write-Host "Installing/updating backend Python dependencies from '$requirementsFile'..." -ForegroundColor Yellow
    if (-not (Test-Path $requirementsFile)) {
        Write-Warning "requirements.txt not found at '$requirementsFile'. Skipping dependency installation."
    } else {
        Push-Location $backendDir
        try {
            # Check pip version first
            $pipVersion = & $venvPythonPath -m pip --version 2>$null
            Write-Host "Current pip version: $pipVersion" -ForegroundColor DarkGray

            Write-Host "Installing Python packages (this may take a few minutes)..." -ForegroundColor DarkGray
            & $venvPythonPath -m pip install -r $requirementsFile --upgrade --log "pip_install.log" --timeout 300

            # Verify critical packages are installed
            $criticalPackages = @("fastapi", "uvicorn", "sqlalchemy", "pydantic")
            foreach ($package in $criticalPackages) {
                $result = & $venvPythonPath -c "import $package; print('OK')" 2>$null
                if ($result -ne "OK") {
                    Write-Warning "Critical package '$package' may not be properly installed."
                }
            }

            Write-Host "Backend dependencies installed/updated successfully." -ForegroundColor Green
        }
        catch {
            Write-Error "Failed to install backend dependencies: $($_.Exception.Message)"
            Write-Host "Check pip_install.log for detailed error information." -ForegroundColor Red
            exit 1
        }
        finally {
            Pop-Location
        }
    }
} else {
    Write-Host "Backend dependencies appear to be installed. Use -ForceInstallBackendDeps to reinstall." -ForegroundColor DarkGray
}

# 3. Start Backend Server
$backendProcess = $null
try {
    Write-Host 'Preparing to start FastAPI backend server...' -ForegroundColor Yellow

    # Determine backend port (8000 or 8001 if 8000 is in use)
    $backendPortToUse = 8000
    if (Test-PortInUse -Port $backendPortToUse) {
        Write-Host "Port $backendPortToUse is already in use. Attempting to stop the process..." -ForegroundColor Yellow
        if (Stop-ProcessUsingPort -Port $backendPortToUse) {
            Write-Host "Successfully stopped process using port $backendPortToUse." -ForegroundColor Green
            Start-Sleep -Seconds 2
        } else {
            Write-Host "Could not stop process using port $backendPortToUse. Trying port 8001 instead." -ForegroundColor Yellow
            $backendPortToUse = 8001
            if (Test-PortInUse -Port $backendPortToUse) {
                Write-Host "Port $backendPortToUse is also in use. Attempting to stop the process..." -ForegroundColor Yellow
                if (Stop-ProcessUsingPort -Port $backendPortToUse) {
                    Write-Host "Successfully stopped process using port $backendPortToUse." -ForegroundColor Green
                    Start-Sleep -Seconds 2
                } else {
                    Write-Host "Could not stop process using port $backendPortToUse either. Backend startup might fail if Uvicorn cannot bind to this port." -ForegroundColor Red
                }
            }
        }
    }

    $script:backendPort = $backendPortToUse

    Write-Host "Attempting to start backend server on port $script:backendPort" -ForegroundColor Yellow
    Write-Host "Command: $venvPythonPath -m uvicorn api.main:app --reload --host 0.0.0.0 --port $script:backendPort --log-level debug" -ForegroundColor Yellow
    Write-Host "Working directory: $backendDir" -ForegroundColor Yellow

    Push-Location $backendDir
    try {
        $backendProcess = Start-Process -FilePath $venvPythonPath -ArgumentList "-m uvicorn api.main:app --reload --host 0.0.0.0 --port $script:backendPort --log-level debug" -WorkingDirectory $backendDir -PassThru -WindowStyle Normal

        Write-Host "Waiting for backend Uvicorn process to stabilize (5s)..." -ForegroundColor DarkGray
        Start-Sleep -Seconds 5

        if ($null -eq $backendProcess -or $backendProcess.HasExited) {
            $exitCodeInfo = if ($backendProcess) { "with code $($backendProcess.ExitCode)" } else { "(process handle is null)" }
            throw "Backend Uvicorn server process exited prematurely $exitCodeInfo. Check the backend console window that may have briefly appeared or closed."
        }

        Write-Host "Backend Uvicorn server process (ID: $($backendProcess.Id)) is running. Verifying application health..." -ForegroundColor Yellow

        # Health check for the backend application
        $healthCheckUrl = "http://localhost:$($script:backendPort)/docs"
        $maxRetries = 5
        $retryDelaySeconds = 4
        $backendHealthy = $false

        for ($attempt = 1; $attempt -le $maxRetries; $attempt++) {
            Write-Host "Attempting backend health check $attempt of $maxRetries to $healthCheckUrl..." -ForegroundColor DarkGray
            try {
                $response = Invoke-WebRequest -Uri $healthCheckUrl -UseBasicParsing -TimeoutSec 7 -ErrorAction Stop
                if ($response.StatusCode -eq 200) {
                    Write-Host "Backend application is healthy (responded with 200 OK from $healthCheckUrl)." -ForegroundColor Green
                    $backendHealthy = $true
                    break
                } else {
                    Write-Host "Backend health check returned non-200 status: $($response.StatusCode) from $healthCheckUrl." -ForegroundColor Yellow
                }
            } catch {
                Write-Host "Backend health check attempt $attempt failed. Error: $($_.Exception.Message)" -ForegroundColor Yellow
                if ($_.Exception.ToString() -like "*No connection could be made because the target machine actively refused it*") {
                    Write-Host "This 'Connection refused' error often means the Uvicorn server process started, but the Python application (api.main) within it FAILED TO LOAD due to an internal error." -ForegroundColor Yellow
                    Write-Host "RECOMMENDATION: Check the backend server's console window (the one Uvicorn is running in) for Python tracebacks (e.g., 'ERROR: Error loading ASGI app. Could not import module api.main')." -ForegroundColor Red
                }
            }

            if (-not $backendHealthy -and $attempt -lt $maxRetries) {
                Write-Host "Waiting $retryDelaySeconds seconds before next health check attempt..." -ForegroundColor DarkGray
                Start-Sleep -Seconds $retryDelaySeconds
            }
        }

        if (-not $backendHealthy) {
            Write-Host "Backend application FAILED to become healthy after $maxRetries attempts at $healthCheckUrl." -ForegroundColor Red
            Write-Host "Please check the backend server's console window for detailed Python errors. The Uvicorn process might still be running but the app is broken." -ForegroundColor Red
            if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) {
                Write-Host "Stopping unresponsive backend Uvicorn process (ID: $($backendProcess.Id))..." -ForegroundColor Yellow
                Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
            }
            throw "Backend application failed to start correctly and become healthy. Review logs in the Uvicorn console window."
        }

        Write-Host "Backend server started successfully on port $script:backendPort and is healthy." -ForegroundColor Green

    }
    catch {
        Write-Host "Error during backend server startup or health check: $($_.Exception.Message)" -ForegroundColor Red
        throw $_
    }
    finally {
        Pop-Location
    }
}
catch {
    Write-Host "CRITICAL ERROR: Backend setup or startup failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) {
        Write-Host "Ensuring backend process (ID: $($backendProcess.Id)) is stopped due to critical error..." -ForegroundColor Yellow
        Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
    }
    exit 1
}

# --- Frontend Setup ---
Write-Host "`n--- Frontend Setup ---" -ForegroundColor Magenta
$npmPath = Find-Executable -CommandName "npm"
if (-not $npmPath) {
    Write-Error "npm (Node Package Manager) not found. Please ensure Node.js and npm are installed and in your PATH."
    if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) {
        Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
    }
    exit 1
}
Write-Host "Using npm: $npmPath"

# 1. Install/Update Frontend Dependencies
$needsFrontendInstall = $ForceInstallFrontendDeps -or (-not (Test-Path (Join-Path $frontendDir "node_modules"))) -or (-not (Test-Path (Join-Path (Join-Path $frontendDir "node_modules") "next")))

if ($needsFrontendInstall) {
    Write-Host "Installing/updating frontend Node.js dependencies in '$frontendDir'..." -ForegroundColor Yellow
    Push-Location $frontendDir
    try {
        Write-Host "Running npm install (this may take a few minutes)..." -ForegroundColor DarkGray
        & $npmPath install --legacy-peer-deps --loglevel=warn

        # Verify critical packages are installed
        if (-not (Test-Path (Join-Path (Join-Path $frontendDir "node_modules") "next"))) {
            Write-Warning "Next.js may not be properly installed."
        }
        if (-not (Test-Path (Join-Path (Join-Path $frontendDir "node_modules") "react"))) {
            Write-Warning "React may not be properly installed."
        }

        Write-Host "Frontend dependencies installed/updated successfully." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to install frontend dependencies: $($_.Exception.Message)"
        if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) {
            Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
        }
        exit 1
    }
    finally {
        Pop-Location
    }
} else {
    Write-Host "Frontend dependencies appear to be installed. Use -ForceInstallFrontendDeps to reinstall." -ForegroundColor DarkGray
}

# 2. Start Frontend Server
$frontendProcess = $null
$frontendPortToUse = 3000
try {
    Write-Host "Preparing to start Next.js frontend server..." -ForegroundColor Yellow
    if (Test-PortInUse -Port $frontendPortToUse) {
        Write-Host "Port $frontendPortToUse is already in use. Attempting to stop the process..." -ForegroundColor Yellow
        if (Stop-ProcessUsingPort -Port $frontendPortToUse) {
            Write-Host "Successfully stopped process using port $frontendPortToUse." -ForegroundColor Green
            Start-Sleep -Seconds 2
        } else {
            Write-Host "Could not stop process using port $frontendPortToUse. Trying port 3001 instead." -ForegroundColor Yellow
            $frontendPortToUse = 3001
            if (Test-PortInUse -Port $frontendPortToUse) {
                Write-Host "Port $frontendPortToUse is also in use. Attempting to stop the process..." -ForegroundColor Yellow
                if (Stop-ProcessUsingPort -Port $frontendPortToUse) {
                    Write-Host "Successfully stopped process using port $frontendPortToUse." -ForegroundColor Green
                    Start-Sleep -Seconds 2
                } else {
                    Write-Host "Could not stop process using port $frontendPortToUse either. Frontend startup might fail." -ForegroundColor Red
                }
            }
        }
    }
    $script:frontendPort = $frontendPortToUse

    Write-Host "Attempting to start frontend server on port $script:frontendPort" -ForegroundColor Yellow
    Push-Location $frontendDir
    try {
        $frontendProcess = Start-Process -FilePath $npmPath -ArgumentList "run dev -- -p $script:frontendPort" -WorkingDirectory $frontendDir -PassThru -WindowStyle Normal
        Write-Host "Waiting for frontend server to become available (15s)..." -ForegroundColor DarkGray
        Start-Sleep -Seconds 15

        # Frontend Health Check
        $frontendHealthCheckUrl = "http://localhost:$($script:frontendPort)"
        $frontendMaxRetries = 2
        $frontendRetryDelaySeconds = 10
        $frontendHealthy = $false

        for ($attempt = 1; $attempt -le $frontendMaxRetries; $attempt++) {
            Write-Host "Attempting frontend health check $attempt of $frontendMaxRetries to $frontendHealthCheckUrl..." -ForegroundColor DarkGray
            try {
                $response = Invoke-WebRequest -Uri $frontendHealthCheckUrl -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
                if ($response.StatusCode -eq 200) {
                    Write-Host "Frontend server is healthy (responded with 200 OK from $frontendHealthCheckUrl)." -ForegroundColor Green
                    $frontendHealthy = $true
                    break
                }
            } catch {
                Write-Host "Frontend health check attempt $attempt failed: $($_.Exception.Message)" -ForegroundColor Yellow
            }

            if (-not $frontendHealthy -and $attempt -lt $frontendMaxRetries) {
                Write-Host "Frontend not healthy. Waiting $frontendRetryDelaySeconds seconds before next check..." -ForegroundColor DarkGray
                Start-Sleep -Seconds $frontendRetryDelaySeconds
            }
        }

        if (-not $frontendHealthy) {
            Write-Warning "Frontend server at $frontendHealthCheckUrl did not become healthy after $frontendMaxRetries attempts."
        } else {
            Write-Host "Frontend server started successfully on port $script:frontendPort and is healthy." -ForegroundColor Green
        }

    } catch {
        Write-Error "Error starting frontend server: $($_.Exception.Message)"
        if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) {
            Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
        }
        exit 1
    }
    finally {
        Pop-Location
    }

}
catch {
    Write-Error "CRITICAL ERROR during frontend setup or startup: $($_.Exception.Message)"
    if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) {
        Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
    }
    if ($frontendProcess -and ($frontendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $frontendProcess.HasExited) {
        Stop-Process -Id $frontendProcess.Id -Force -ErrorAction SilentlyContinue
    }
    exit 1
}

# --- Application Running ---
Write-Host "`n--- Application Status ---" -ForegroundColor Magenta
Write-Host "Backend API should be running at: http://localhost:$($script:backendPort)/docs" -ForegroundColor Green
Write-Host "Frontend App should be running at: http://localhost:$($script:frontendPort)" -ForegroundColor Green

# Open in browser
$frontendUrl = "http://localhost:$($script:frontendPort)"
Write-Host "Opening $frontendUrl in your default browser..."
try {
    Start-Process $frontendUrl
}
catch {
    Write-Warning "Could not open URL in browser: $($_.Exception.Message)"
}

Write-Host "`nJoMaDe application startup sequence complete." -ForegroundColor Cyan
Write-Host "Backend and Frontend servers are running in separate console windows."
Write-Host "You can view their logs in those windows."
Write-Host "Backend API Docs: http://localhost:$($script:backendPort)/docs"
Write-Host "Frontend App:     http://localhost:$($script:frontendPort)"

# --- Wait for user to stop ---
Write-Host "`nPress Enter to stop servers..." -ForegroundColor Yellow
Read-Host

# --- Shutdown ---
Write-Host "`nStopping JoMaDe Application..." -ForegroundColor Cyan

if ($frontendProcess -and ($frontendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $frontendProcess.HasExited) {
    Write-Host "Stopping frontend server (PID: $($frontendProcess.Id))..."
    try {
        Stop-Process -Id $frontendProcess.Id -Force -ErrorAction Stop
        Write-Host "Frontend server stopped." -ForegroundColor Green
    }
    catch {
        Write-Warning "Could not stop frontend server (PID: $($frontendProcess.Id)): $($_.Exception.Message)"
    }
} else {
    Write-Host "Frontend server process not found or already stopped." -ForegroundColor DarkGray
}

if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) {
    Write-Host "Stopping backend server (PID: $($backendProcess.Id))..."
    try {
        Stop-Process -Id $backendProcess.Id -Force -ErrorAction Stop
        Write-Host "Backend server stopped." -ForegroundColor Green
    }
    catch {
        Write-Warning "Could not stop backend server (PID: $($backendProcess.Id)): $($_.Exception.Message)"
    }
} else {
    Write-Host "Backend server process not found or already stopped." -ForegroundColor DarkGray
}

Write-Host "JoMaDe has been stopped." -ForegroundColor Cyan
Write-Host "Press any key to exit..." -ForegroundColor Yellow
[void][System.Console]::ReadKey($true)