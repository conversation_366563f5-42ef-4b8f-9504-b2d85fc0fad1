import os
import sys
import json
import datetime
import logging
import tempfile
import shutil
from typing import List, Dict, Any, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import required libraries
try:
    import openai
    from langchain.document_loaders import (
        PyPDFLoader,
        TextLoader,
        Docx2txtLoader,
        UnstructuredFileLoader
    )
    from langchain.text_splitter import RecursiveCharacterTextSplitter
    from langchain.embeddings import OpenAIEmbeddings
    from langchain.vectorstores import FAISS
except ImportError as e:
    logger.error(f"Required libraries not installed: {str(e)}")
    logger.error("Please install required libraries: pip install langchain openai faiss-cpu pypdf docx2txt unstructured")
    raise

def create_embedding(
    file_paths: List[str],
    embedding_name: str,
    model: str = "text-embedding-ada-002",
    output_dir: str = None,
    chunk_size: int = 1000,
    chunk_overlap: int = 200
) -> Tuple[str, str]:
    """
    Create embeddings from files and save to FAISS index
    
    Args:
        file_paths: List of file paths to process
        embedding_name: Name for the embedding
        model: OpenAI embedding model to use
        output_dir: Directory to save embeddings
        chunk_size: Size of text chunks
        chunk_overlap: Overlap between chunks
        
    Returns:
        Tuple of (faiss_path, json_path)
    """
    logger.info(f"Creating embedding from {len(file_paths)} files using model {model}")
    
    # Create output directory if it doesn't exist
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "embeddings")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load documents
    documents = []
    file_info = []
    
    for file_path in file_paths:
        try:
            logger.info(f"Processing file: {file_path}")
            
            # Get file extension
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()
            
            # Load document based on file type
            if ext == '.pdf':
                loader = PyPDFLoader(file_path)
            elif ext == '.txt':
                loader = TextLoader(file_path)
            elif ext in ['.docx', '.doc']:
                loader = Docx2txtLoader(file_path)
            else:
                # Try to use unstructured for other file types
                loader = UnstructuredFileLoader(file_path)
            
            # Load document
            file_docs = loader.load()
            
            # Add to documents list
            documents.extend(file_docs)
            
            # Add file info
            file_info.append({
                'filename': os.path.basename(file_path),
                'path': file_path,
                'size': os.path.getsize(file_path),
                'pages': len(file_docs)
            })
            
            logger.info(f"Successfully loaded {len(file_docs)} pages from {file_path}")
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {str(e)}")
    
    if not documents:
        logger.error("No documents were loaded")
        raise ValueError("No documents were loaded")
    
    # Split documents into chunks
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap
    )
    
    chunks = text_splitter.split_documents(documents)
    logger.info(f"Split documents into {len(chunks)} chunks")
    
    # Create embeddings
    embeddings = OpenAIEmbeddings(model=model)
    
    # Create FAISS index
    vectorstore = FAISS.from_documents(chunks, embeddings)
    
    # Save FAISS index
    date_str = datetime.datetime.now().strftime("%Y%m%d")
    faiss_path = os.path.join(output_dir, f"{embedding_name}.faiss")
    json_path = os.path.join(output_dir, f"{embedding_name}.json")
    
    # Save FAISS index
    vectorstore.save_local(faiss_path)
    logger.info(f"Saved FAISS index to {faiss_path}")
    
    # Save metadata
    metadata = {
        'date': date_str,
        'model': model,
        'user': 'User',
        'chunk_size': chunk_size,
        'chunk_overlap': chunk_overlap,
        'files': file_info
    }
    
    with open(json_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    logger.info(f"Saved metadata to {json_path}")
    
    return faiss_path, json_path
