'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Box, Heading, Text, Button, VStack, Code, Alert, AlertIcon, AlertTitle, AlertDescription } from '@chakra-ui/react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI.
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Box p={6} borderRadius="lg" bg="white" boxShadow="md" maxW="800px" mx="auto" my={8}>
          <Alert status="error" variant="subtle" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" borderRadius="md" mb={6}>
            <AlertIcon boxSize="40px" mr={0} />
            <AlertTitle mt={4} mb={1} fontSize="lg">
              Something went wrong
            </AlertTitle>
            <AlertDescription maxWidth="sm">
              The application encountered an unexpected error.
            </AlertDescription>
          </Alert>

          <VStack align="stretch" spacing={4}>
            <Box>
              <Heading size="md" mb={2}>Error Details</Heading>
              <Code p={3} borderRadius="md" variant="subtle" display="block" whiteSpace="pre-wrap">
                {this.state.error?.toString()}
              </Code>
            </Box>

            {this.state.errorInfo && (
              <Box>
                <Heading size="md" mb={2}>Component Stack</Heading>
                <Code p={3} borderRadius="md" variant="subtle" display="block" whiteSpace="pre-wrap" maxH="300px" overflowY="auto">
                  {this.state.errorInfo.componentStack}
                </Code>
              </Box>
            )}

            <Button colorScheme="blue" onClick={this.handleReset} alignSelf="center" mt={4}>
              Try Again
            </Button>
          </VStack>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
