from fastapi import APIRouter, HTTPException, Depends, Body
from typing import Dict, Any, List
import os
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Function to generate a three-letter prefix (AAA, AAB, ..., ZZZ) from an ID
def generate_prefix_from_id(id: int) -> str:
    """
    Generate a three-letter prefix (AAA, AAB, AAC, etc.) from an ID.
    This supports up to 17,576 different job sources (26^3).

    Args:
        id: The ID to convert (0-based)

    Returns:
        A three-letter prefix (e.g., "AAA", "AAB", "AAC", etc.)
    """
    # Ensure ID is positive
    id = max(0, id)

    # Convert to base-26 (A-Z) with 3 digits
    first_char = chr(65 + (id // 676) % 26)  # 26^2 = 676
    second_char = chr(65 + (id // 26) % 26)
    third_char = chr(65 + id % 26)

    return f"{first_char}{second_char}{third_char}"

# Create router
router = APIRouter(
    prefix="/api/v1/job-url-md",
    tags=["job-url-md"],
    responses={404: {"description": "Not found"}},
)

# Path to job_url.md - using lowercase version
# Calculate the path step by step for better debugging
current_file = __file__
logger.info(f"Current file path: {current_file}")

api_routes_dir = os.path.dirname(current_file)
logger.info(f"API routes directory: {api_routes_dir}")

api_dir = os.path.dirname(api_routes_dir)
logger.info(f"API directory: {api_dir}")

backend_dir = os.path.dirname(api_dir)
logger.info(f"Backend directory: {backend_dir}")

JOB_URL_PATH = os.path.join(backend_dir, "job_url.md")
logger.info(f"Calculated job_url.md path: {JOB_URL_PATH}")

# Check if the file exists during module initialization
if os.path.exists(JOB_URL_PATH):
    logger.info(f"job_url.md file exists at: {JOB_URL_PATH}")
    # Log the first few lines for debugging
    try:
        with open(JOB_URL_PATH, 'r', encoding='utf-8') as file:
            first_lines = [next(file) for _ in range(5)]
            logger.info(f"First 5 lines of job_url.md: {first_lines}")
    except Exception as e:
        logger.error(f"Error reading job_url.md: {str(e)}")
else:
    logger.error(f"job_url.md file NOT FOUND at: {JOB_URL_PATH}")

@router.get("/")
async def get_job_urls():
    """
    Get job URLs from job_url.md
    """
    try:
        logger.info(f"Loading job URLs from: {JOB_URL_PATH}")

        # Check if file exists
        if not os.path.exists(JOB_URL_PATH):
            logger.error(f"File not found: {JOB_URL_PATH}")
            raise HTTPException(status_code=404, detail="job_url.md not found")

        # Read the job URLs from the file
        with open(JOB_URL_PATH, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # Convert to list for API response
        sources = []
        url_idx = 0  # Separate index for actual URLs

        # Log the total number of lines in the file
        logger.info(f"Total lines in job_url.md: {len(lines)}")

        # Start from line 11 as per the memory (lines 1-10 are comments)
        start_line = 10  # 0-indexed, so line 11 is at index 10

        # Process lines starting from line 11, skipping comments and empty lines
        for idx, line in enumerate(lines[start_line:], start=start_line):
            url = line.strip()
            # Skip empty lines and comment lines (starting with #)
            if url and not url.startswith('#'):
                # Generate prefix based on the current URL index
                prefix = generate_prefix_from_id(url_idx)
                # Log the prefix for debugging
                logger.info(f"Generated prefix for URL {url_idx}: {prefix}")
                url_idx += 1  # Increment URL index only for actual URLs
                sources.append({
                    "id": str(url_idx),
                    "url": url,
                    "is_active": True,
                    "prefix": prefix
                })

        logger.info(f"Successfully loaded {len(sources)} job URLs")
        # Log the sources for debugging
        logger.info(f"Sources with prefixes: {sources}")
        return sources

    except Exception as e:
        logger.error(f"Error loading job URLs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading job URLs: {str(e)}")

@router.post("/")
async def add_job_url(data: Dict[str, Any] = Body(...)):
    """
    Add a new job URL to job_url.md
    """
    try:
        url = data.get("url")
        if not url:
            raise HTTPException(status_code=400, detail="URL is required")

        logger.info(f"Adding job URL: {url}")

        # Check if file exists
        if not os.path.exists(JOB_URL_PATH):
            logger.info(f"Creating new job_url.md file")
            with open(JOB_URL_PATH, 'w', encoding='utf-8') as file:
                file.write(f"{url}\n")

            logger.info(f"Successfully added job URL: {url}")
            return {"message": "Job URL added successfully"}

        # Read existing URLs
        with open(JOB_URL_PATH, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # Check if URL already exists
        existing_urls = [line.strip() for line in lines]
        if url in existing_urls:
            logger.warning(f"URL already exists: {url}")
            return {"message": "URL already exists"}

        # Add the new URL
        with open(JOB_URL_PATH, 'a', encoding='utf-8') as file:
            # Add a newline if the file doesn't end with one
            if lines and not lines[-1].endswith('\n'):
                file.write('\n')
            file.write(f"{url}\n")

        logger.info(f"Successfully added job URL: {url}")
        return {"message": "Job URL added successfully"}

    except Exception as e:
        logger.error(f"Error adding job URL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding job URL: {str(e)}")

@router.delete("/{url_id}")
async def delete_job_url(url_id: str):
    """
    Delete a job URL from job_url.md
    """
    try:
        logger.info(f"Deleting job URL with ID: {url_id}")

        # Check if file exists
        if not os.path.exists(JOB_URL_PATH):
            logger.error(f"File not found: {JOB_URL_PATH}")
            raise HTTPException(status_code=404, detail="job_url.md not found")

        # Read existing URLs
        with open(JOB_URL_PATH, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # Find the line corresponding to the URL ID
        try:
            url_id_int = int(url_id)

            # Find the line that corresponds to the URL ID
            url_idx = 0
            line_idx = -1

            for idx, line in enumerate(lines):
                url = line.strip()
                if url and not url.startswith('#'):
                    url_idx += 1
                    if url_idx == url_id_int:
                        line_idx = idx
                        break

            if line_idx == -1:
                raise ValueError(f"URL with ID {url_id} not found")

            # Set idx to the actual line index
            idx = line_idx
        except ValueError:
            logger.error(f"Invalid URL ID: {url_id}")
            raise HTTPException(status_code=400, detail="Invalid URL ID")

        # Remove the URL
        deleted_url = lines[idx].strip()
        lines.pop(idx)

        # Write the updated URLs
        with open(JOB_URL_PATH, 'w', encoding='utf-8') as file:
            file.writelines(lines)

        logger.info(f"Successfully deleted job URL: {deleted_url}")
        return {"message": f"Job URL deleted successfully: {deleted_url}"}

    except Exception as e:
        logger.error(f"Error deleting job URL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting job URL: {str(e)}")
