# JoMaDe Application Runner Script
# Simplified version for testing

# Define simple function
function Write-Message {
    param (
        [string]$Message
    )
    Write-Host $Message -ForegroundColor Cyan
}

# Main execution
try {
    Write-Message "Starting JoMaDe Application Test"
    Write-Host "This is a test script" -ForegroundColor Green
    Write-Host "Press Enter to exit..." -ForegroundColor Yellow
    Read-Host
}
finally {
    Write-Host "Exiting..." -ForegroundColor Yellow
} 