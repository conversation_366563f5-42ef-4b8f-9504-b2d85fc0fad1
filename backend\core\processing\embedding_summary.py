import openai
from pathlib import Path
import faiss
import numpy as np
import json

def get_embedding_content():
    """Retrieve the content from the latest embedding files."""
    try:
        embed_dir = Path(__file__).parent.parent / "data" / "embeddings"
        faiss_files = list(embed_dir.glob("*.faiss"))
        json_files = list(embed_dir.glob("*.json"))
        
        if not faiss_files or not json_files:
            print("❌ No embeddings found. Please create embeddings first.")
            return None
            
        # Get latest JSON file
        latest_json = max(json_files, key=lambda f: f.stat().st_ctime)
        
        # Load metadata and content
        with open(latest_json, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
            
        return metadata
        
    except Exception as e:
        print(f"❌ Error retrieving embedding content: {str(e)}")
        return None

def generate_cv_summary_test(metadata: dict, temperature: float = 0.3) -> str:
    """Test function to generate CV summary from embedding content."""
    try:
        # Extract the actual CV content from metadata
        extracted_files = metadata.get('files', [])
        print(f"\nFound {len(extracted_files)} source files in embeddings:")
        for file in extracted_files:
            print(f"- {file}")
            
        # Get the extracted text content
        text_dir = Path(__file__).parent.parent / "data" / "extracted_text"
        cv_content = ""
        for file in text_dir.glob("*_extracted.txt"):
            print(f"\nReading: {file.name}")
            with open(file, 'r', encoding='utf-8') as f:
                cv_content += f.read() + "\n\n"

        if not cv_content.strip():
            print("❌ No CV content found in extracted text files")
            return None

        system_prompt = """You are a professional career advisor and executive search consultant, specialized in the German 
        labor market, particularly in high-profile executive and management positions. You have deep knowledge of German 
        corporate culture, industry standards, and the executive search process.

        YOUR TASK:
        Based on the provided CV content, create an anonymous professional summary that includes:
        1. Core professional experience and expertise (without naming specific companies)
        2. Key industries and company types (e.g., Mittelstand, corporate, industry sectors)
        3. Leadership and management experience (scope and scale)
        4. Technical skills and implementations (systems, methods, tools)
        5. Notable achievements with metrics (numbers, percentages, scale)

        FORMAT:
        Return a clear, concise summary (max 250 words) focusing on:
        - Career level and years of experience
        - Main functional areas
        - Industry sectors (e.g., manufacturing, automotive, technology)
        - Company types (e.g., Mittelstand, corporate, global)
        - Leadership scope (team sizes, budget responsibility)
        - Key achievements with numbers
        - Technical implementations
        - Do NOT include:
          * Personal names
          * Company names
          * Specific locations
          * HR/HRA/HRB references

        EXAMPLE STYLE:
        Senior executive with 15+ years in manufacturing industry, primarily in German Mittelstand companies. 
        Led operations in automotive supplier sector with €50M P&L responsibility. Implemented SAP S/4HANA 
        across 3 production sites, managing teams of 50+ staff."""

        print("\nGenerating summary from CV content...")
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Create an anonymous CV summary based on this content:\n\n{cv_content}"}
            ],
            temperature=temperature
        )
        
        summary = response.choices[0].message.content.strip()
        print("\n✅ Generated CV summary from embeddings (TEST)")
        return summary
        
    except Exception as e:
        print(f"❌ Error generating CV summary (TEST): {str(e)}")
        print(f"Error details: {str(e)}")
        return None

def test_cv_summary_from_embeddings(temperature: float = 0.3) -> str:
    """Test function to create CV summary from embeddings."""
    try:
        print("\n=== Testing CV Summary Generation ===")
        # Get embedding content
        metadata = get_embedding_content()
        if not metadata:
            return None
            
        # Generate summary
        summary = generate_cv_summary_test(metadata, temperature)
        if summary:
            print("\n=== Generated Summary ===")
            print(summary)
            print("\n=== End Summary ===")
        return summary
        
    except Exception as e:
        print(f"❌ Error in test_cv_summary_from_embeddings: {str(e)}")
        return None

# Test function
if __name__ == "__main__":
    print("=== Running Embedding Summary Test ===")
    summary = test_cv_summary_from_embeddings(temperature=0.3)
    if summary:
        print("\nTest completed successfully!")
    else:
        print("\nTest failed!") 