'use client';

import React, { useState } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  Heading,
  Flex,
  Input,
  Select,
  FormControl,
  FormLabel,
  useToast,
  Alert,
  AlertIcon,
  InputGroup,
  InputRightElement,
} from '@chakra-ui/react';

interface JobSourceSelectionProps {
  onSourceChange?: (source: string) => void;
  onUrlAdded?: (url: string) => void;
}

const JobSourceSelection: React.FC<JobSourceSelectionProps> = ({ onSourceChange, onUrlAdded }) => {
  const [selectedSource, setSelectedSource] = useState<string>('excel');
  const [url, setUrl] = useState<string>('');
  const [isAddingUrl, setIsAddingUrl] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();

  // Handle source change
  const handleSourceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const source = e.target.value;
    setSelectedSource(source);

    if (onSourceChange) {
      onSourceChange(source);
    }
  };

  // Handle URL input change
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(e.target.value);
  };

  // Handle adding URL
  const handleAddUrl = async () => {
    if (!url) {
      setError('Please enter a URL');
      return;
    }

    setIsAddingUrl(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:8000/api/v1/job-sources', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url,
          source_type: selectedSource,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to add URL: ${response.status}`);
      }

      const result = await response.json();

      toast({
        title: 'URL Added',
        description: `Successfully added job source URL: ${url}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Clear URL after successful addition
      setUrl('');

      if (onUrlAdded) {
        onUrlAdded(url);
      }
    } catch (err) {
      console.error('Error adding URL:', err);
      setError(err instanceof Error ? err.message : 'Failed to add URL');

      toast({
        title: 'Failed to Add URL',
        description: err instanceof Error ? err.message : 'Failed to add URL',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsAddingUrl(false);
    }
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="white">
      <VStack align="stretch" spacing={4}>
        <Heading size="md">Job Source Selection</Heading>

        <FormControl>
          <FormLabel>Job Source Method</FormLabel>
          <Select value={selectedSource} onChange={handleSourceChange}>
            <option value="excel">Excel File</option>
            <option value="kadoa">Use Kadoa</option>
          </Select>
        </FormControl>

        <FormControl>
          <FormLabel>Job Posting URL</FormLabel>
          <InputGroup>
            <Input
              value={url}
              onChange={handleUrlChange}
              placeholder="Enter job posting URL"
              isDisabled={isAddingUrl}
            />
            <InputRightElement width="4.5rem">
              <Button
                h="1.75rem"
                size="sm"
                onClick={handleAddUrl}
                isLoading={isAddingUrl}
                isDisabled={!url}
              >
                Add
              </Button>
            </InputRightElement>
          </InputGroup>
        </FormControl>

        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}
      </VStack>
    </Box>
  );
};

export default JobSourceSelection;
