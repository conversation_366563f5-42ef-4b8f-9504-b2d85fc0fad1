from sqlalchemy import Column, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JSO<PERSON>, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime

from ..db.base_class import Base


class KadoaWorkflow(Base):
    """Model for storing Kadoa workflows"""
    __tablename__ = "kadoa_workflows"

    id = Column(String, primary_key=True)
    url = Column(String, nullable=False)
    name = Column(String, nullable=True)
    state = Column(String, nullable=False)
    schema_type = Column(String, default="DETAIL")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_sync_at = Column(DateTime, nullable=True)
    job_count = Column(Integer, default=0)
    
    # Relationships
    jobs = relationship("KadoaJob", back_populates="workflow", cascade="all, delete-orphan")


class KadoaJob(Base):
    """Model for storing Kadoa jobs"""
    __tablename__ = "kadoa_jobs"

    id = Column(String, primary_key=True)
    workflow_id = Column(String, ForeignKey("kadoa_workflows.id"), nullable=False)
    external_id = Column(String, nullable=False)
    title = Column(String, nullable=False)
    company = Column(String, nullable=False)
    location = Column(String)
    description = Column(String)
    salary_range = Column(JSON)
    employment_type = Column(String)
    experience_level = Column(String)
    required_skills = Column(JSON)
    preferred_skills = Column(JSON)
    education = Column(String)
    link = Column(String)
    reference = Column(String)
    remote_work = Column(Boolean)
    source = Column(String, default="Kadoa")
    raw_data = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    scraped_at = Column(DateTime)
    is_processed = Column(Boolean, default=False)
    processing_status = Column(String)
    processing_error = Column(String)
    
    # Evaluation fields
    is_shortlisted = Column(Boolean, default=False)
    shortlist_score = Column(Integer, default=0)
    shortlist_timestamp = Column(DateTime)
    is_evaluated = Column(Boolean, default=False)
    evaluation_score = Column(Integer, default=0)
    evaluation_pros = Column(String)
    evaluation_cons = Column(String)
    evaluation_timestamp = Column(DateTime)
    
    # Display fields
    display_color = Column(String, default="none")
    display_status = Column(String, default="none")
    display_text = Column(String)
    
    # Relationships
    workflow = relationship("KadoaWorkflow", back_populates="jobs") 