'use client';

import React, { useState, useEffect } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  Button,
  Text,
  Progress,
  VStack,
  Box,
  useDisclosure,
  CloseButton,
} from '@chakra-ui/react';

interface ProgressDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  initialMessage?: string;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const ProgressDialog: React.FC<ProgressDialogProps> = ({
  isOpen,
  onClose,
  title,
  initialMessage = 'Starting process...',
  autoClose = true,
  autoCloseDelay = 1500,
}) => {
  const [message, setMessage] = useState<string>(initialMessage);
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(true);
  const [messages, setMessages] = useState<string[]>([initialMessage]);

  // Reset state when dialog opens
  useEffect(() => {
    if (isOpen) {
      setMessage(initialMessage);
      setIsComplete(false);
      setIsSuccess(true);
      setMessages([initialMessage]);
    }
  }, [isOpen, initialMessage]);

  // Auto-close when complete if autoClose is true
  useEffect(() => {
    if (isComplete && autoClose) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [isComplete, autoClose, onClose, autoCloseDelay]);

  // Function to update progress message
  const updateMessage = (newMessage: string) => {
    setMessage(newMessage);
    setMessages(prev => [...prev, newMessage]);
  };

  // Function to mark process as complete
  const markComplete = (success: boolean = true) => {
    setIsComplete(true);
    setIsSuccess(success);
    updateMessage(success ? '✅ Process completed successfully!' : '❌ Process failed!');
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} closeOnOverlayClick={isComplete} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{title}</ModalHeader>
        {isComplete && <CloseButton position="absolute" right="8px" top="8px" onClick={onClose} />}

        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Text fontWeight="bold">{message}</Text>

            {!isComplete ? (
              <Progress size="md" isIndeterminate colorScheme="blue" />
            ) : (
              <Progress size="md" value={100} colorScheme={isSuccess ? 'green' : 'red'} />
            )}

            {messages.length > 1 && (
              <Box
                mt={2}
                maxHeight="150px"
                overflowY="auto"
                p={2}
                borderWidth="1px"
                borderRadius="md"
                fontSize="sm"
              >
                {messages.map((msg, index) => (
                  <Text key={index} mb={1}>
                    {msg}
                  </Text>
                ))}
              </Box>
            )}
          </VStack>
        </ModalBody>

        <ModalFooter>
          {isComplete && (
            <Button colorScheme={isSuccess ? 'green' : 'red'} onClick={onClose}>
              Close
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

// Create a hook to easily use the ProgressDialog
export const useProgressDialog = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [title, setTitle] = useState<string>('Processing...');
  const [initialMessage, setInitialMessage] = useState<string>('Starting process...');
  const [messageRef, setMessageRef] = useState<React.MutableRefObject<string>>(
    React.createRef() as React.MutableRefObject<string>
  );
  const [completeRef, setCompleteRef] = useState<
    React.MutableRefObject<(success: boolean) => void>
  >(React.createRef() as React.MutableRefObject<(success: boolean) => void>);

  const ProgressDialogComponent = (
    <ProgressDialog
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      initialMessage={initialMessage}
    />
  );

  const openProgressDialog = (
    dialogTitle: string,
    dialogInitialMessage: string = 'Starting process...'
  ) => {
    setTitle(dialogTitle);
    setInitialMessage(dialogInitialMessage);
    onOpen();
  };

  return {
    ProgressDialog: ProgressDialogComponent,
    openProgressDialog,
    closeProgressDialog: onClose,
    isProgressDialogOpen: isOpen,
    updateMessage: (message: string) => {
      if (messageRef.current) {
        messageRef.current = message;
      }
    },
    markComplete: (success: boolean = true) => {
      if (completeRef.current) {
        completeRef.current(success);
      }
    },
  };
};

export default ProgressDialog;
