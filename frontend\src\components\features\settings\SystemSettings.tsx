'use client';

import React, { useState } from 'react';
import {
  Box,
  VStack,
  Button,
  FormControl,
  FormLabel,
  Input,
  FormHelperText,
  useToast,
  Divider,
  Text,
  HStack,
  Select,
  Switch,
  InputGroup,
  InputRightElement,
  IconButton,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  SliderMark,
  Tooltip,
  useColorModeValue,
} from '@chakra-ui/react';
import { FiEye, FiEyeOff, FiSave, FiRefreshCw } from 'react-icons/fi';
import { Card } from '../../common';

const SystemSettings: React.FC = () => {
  // API Keys
  const [openaiKey, setOpenaiKey] = useState<string>(
    'sk-••••••••••••••••••••••••••••••••••••••••••••••'
  );
  const [kadoa<PERSON>ey, setK<PERSON>aKey] = useState<string>(
    'kad-••••••••••••••••••••••••••••••••••••••••••••••'
  );
  const [firecrawlKey, setFirecrawlKey] = useState<string>(
    'fc-••••••••••••••••••••••••••••••••••••••••••••••'
  );

  // Show/hide passwords
  const [showOpenaiKey, setShowOpenaiKey] = useState<boolean>(false);
  const [showKadoaKey, setShowKadoaKey] = useState<boolean>(false);
  const [showFirecrawlKey, setShowFirecrawlKey] = useState<boolean>(false);

  // Model settings
  const [defaultModel, setDefaultModel] = useState<string>('gpt-4o');
  const [temperature, setTemperature] = useState<number>(0.7);
  const [showTempTooltip, setShowTempTooltip] = useState<boolean>(false);
  const [threshold, setThreshold] = useState<number>(70);
  const [showThresholdTooltip, setShowThresholdTooltip] = useState<boolean>(false);

  // Other settings
  const [enableLogging, setEnableLogging] = useState<boolean>(true);
  const [enableNotifications, setEnableNotifications] = useState<boolean>(true);

  const toast = useToast();
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Handle saving settings
  const handleSaveSettings = () => {
    // In a real implementation, we would call the API
    toast({
      title: 'Settings saved',
      description: 'Your system settings have been updated',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  // Handle testing API keys
  const handleTestApiKeys = () => {
    // In a real implementation, we would call the API to test the keys
    toast({
      title: 'API keys tested',
      description: 'All API keys are valid and working',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <VStack spacing={6} align="stretch">
      {/* API Keys Section */}
      <Card title="API Keys" subtitle="Configure API keys for external services">
        <VStack spacing={4} align="stretch">
          <FormControl>
            <FormLabel>OpenAI API Key</FormLabel>
            <InputGroup>
              <Input
                type={showOpenaiKey ? 'text' : 'password'}
                value={openaiKey}
                onChange={e => setOpenaiKey(e.target.value)}
                placeholder="sk-..."
              />
              <InputRightElement>
                <IconButton
                  aria-label={showOpenaiKey ? 'Hide API key' : 'Show API key'}
                  icon={showOpenaiKey ? <FiEyeOff /> : <FiEye />}
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowOpenaiKey(!showOpenaiKey)}
                />
              </InputRightElement>
            </InputGroup>
            <FormHelperText>Used for LLM-based job matching and evaluation</FormHelperText>
          </FormControl>

          <FormControl>
            <FormLabel>Kadoa API Key</FormLabel>
            <InputGroup>
              <Input
                type={showKadoaKey ? 'text' : 'password'}
                value={kadoaKey}
                onChange={e => setKadoaKey(e.target.value)}
                placeholder="kad-..."
              />
              <InputRightElement>
                <IconButton
                  aria-label={showKadoaKey ? 'Hide API key' : 'Show API key'}
                  icon={showKadoaKey ? <FiEyeOff /> : <FiEye />}
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowKadoaKey(!showKadoaKey)}
                />
              </InputRightElement>
            </InputGroup>
            <FormHelperText>Used for job scraping with Kadoa</FormHelperText>
          </FormControl>

          <FormControl>
            <FormLabel>Firecrawl API Key</FormLabel>
            <InputGroup>
              <Input
                type={showFirecrawlKey ? 'text' : 'password'}
                value={firecrawlKey}
                onChange={e => setFirecrawlKey(e.target.value)}
                placeholder="fc-..."
              />
              <InputRightElement>
                <IconButton
                  aria-label={showFirecrawlKey ? 'Hide API key' : 'Show API key'}
                  icon={showFirecrawlKey ? <FiEyeOff /> : <FiEye />}
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFirecrawlKey(!showFirecrawlKey)}
                />
              </InputRightElement>
            </InputGroup>
            <FormHelperText>Used for job scraping with Firecrawl</FormHelperText>
          </FormControl>

          <Button
            leftIcon={<FiRefreshCw />}
            colorScheme="blue"
            variant="outline"
            onClick={handleTestApiKeys}
          >
            Test API Keys
          </Button>
        </VStack>
      </Card>

      {/* Model Settings Section */}
      <Card title="Model Settings" subtitle="Configure AI model parameters">
        <VStack spacing={4} align="stretch">
          <FormControl>
            <FormLabel>Default LLM Model</FormLabel>
            <Select value={defaultModel} onChange={e => setDefaultModel(e.target.value)}>
              <option value="gpt-4o">GPT-4o</option>
              <option value="gpt-4-turbo">GPT-4 Turbo</option>
              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
            </Select>
            <FormHelperText>
              The default model to use for job matching and evaluation
            </FormHelperText>
          </FormControl>

          <FormControl>
            <FormLabel>Temperature: {temperature.toFixed(1)}</FormLabel>
            <Slider
              min={0}
              max={1}
              step={0.1}
              value={temperature}
              onChange={setTemperature}
              onMouseEnter={() => setShowTempTooltip(true)}
              onMouseLeave={() => setShowTempTooltip(false)}
            >
              <SliderTrack>
                <SliderFilledTrack />
              </SliderTrack>
              <Tooltip
                hasArrow
                bg="blue.500"
                color="white"
                placement="top"
                isOpen={showTempTooltip}
                label={`Temperature: ${temperature.toFixed(1)}`}
              >
                <SliderThumb />
              </Tooltip>
            </Slider>
            <FormHelperText>
              Controls randomness in model responses (0 = deterministic, 1 = creative)
            </FormHelperText>
          </FormControl>

          <FormControl>
            <FormLabel>Match Threshold: {threshold}%</FormLabel>
            <Slider
              min={0}
              max={100}
              step={5}
              value={threshold}
              onChange={setThreshold}
              onMouseEnter={() => setShowThresholdTooltip(true)}
              onMouseLeave={() => setShowThresholdTooltip(false)}
            >
              <SliderTrack>
                <SliderFilledTrack />
              </SliderTrack>
              <Tooltip
                hasArrow
                bg="blue.500"
                color="white"
                placement="top"
                isOpen={showThresholdTooltip}
                label={`Threshold: ${threshold}%`}
              >
                <SliderThumb />
              </Tooltip>
            </Slider>
            <FormHelperText>Minimum match score for job shortlisting</FormHelperText>
          </FormControl>
        </VStack>
      </Card>

      {/* Application Settings Section */}
      <Card title="Application Settings" subtitle="Configure general application settings">
        <VStack spacing={4} align="stretch">
          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="enable-logging" mb="0">
              Enable Logging
            </FormLabel>
            <Switch
              id="enable-logging"
              isChecked={enableLogging}
              onChange={e => setEnableLogging(e.target.checked)}
              colorScheme="blue"
            />
          </FormControl>

          <FormHelperText pl={0}>Log application events for troubleshooting</FormHelperText>

          <Divider />

          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="enable-notifications" mb="0">
              Enable Notifications
            </FormLabel>
            <Switch
              id="enable-notifications"
              isChecked={enableNotifications}
              onChange={e => setEnableNotifications(e.target.checked)}
              colorScheme="blue"
            />
          </FormControl>

          <FormHelperText pl={0}>Show notifications for important events</FormHelperText>
        </VStack>
      </Card>

      {/* Save Button */}
      <Button leftIcon={<FiSave />} colorScheme="blue" size="lg" onClick={handleSaveSettings}>
        Save All Settings
      </Button>
    </VStack>
  );
};

export default SystemSettings;
