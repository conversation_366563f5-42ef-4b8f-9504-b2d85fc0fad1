import create from 'zustand';

interface Workflow {
  id: string;
  url: string;
  state: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'PAUSED' | 'ACTIVE';
  job_count?: number;
}

interface KadoaStore {
  workflows: Workflow[];
  isLoading: boolean;
  error: string | null;
  setWorkflows: (workflows: Workflow[]) => void;
  addWorkflow: (workflow: Workflow) => void;
  updateWorkflow: (id: string, updates: Partial<Workflow>) => void;
  removeWorkflow: (id: string) => void;
}

export const useKadoaStore = create<KadoaStore>(set => ({
  workflows: [],
  isLoading: false,
  error: null,
  setWorkflows: workflows => set({ workflows }),
  addWorkflow: workflow => set(state => ({ workflows: [...state.workflows, workflow] })),
  updateWorkflow: (id, updates) =>
    set(state => ({
      workflows: state.workflows.map(w => (w.id === id ? { ...w, ...updates } : w)),
    })),
  removeWorkflow: id =>
    set(state => ({
      workflows: state.workflows.filter(w => w.id !== id),
    })),
}));
