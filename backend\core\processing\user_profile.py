import pandas as pd
import os

def get_cv_summary(job_sites_path):
    """
    Load CV summary from job_sites.xlsx, sheet 'CV', cell B2
    
    Args:
        job_sites_path: Path to the job_sites.xlsx file
        
    Returns:
        str: CV summary text
    """
    try:
        print(f"Loading CV summary from: {job_sites_path}")
        
        # Read the CV sheet from Excel
        df = pd.read_excel(job_sites_path, sheet_name="CV", header=None)  # Added header=None
        
        # Get the CV summary from cell B2 (index [1,1])
        cv_summary = str(df.iloc[1, 1]).strip()  # B2 is row 2, column B
        
        if pd.isna(cv_summary):
            print("❌ ERROR: CV summary cell (B2) is empty")
            return ""
            
        print(f"Successfully loaded CV summary ({len(cv_summary)} characters)")
        return cv_summary
        
    except Exception as e:
        print(f"Error loading CV summary: {e}")
        print(f"DataFrame shape: {df.shape if 'df' in locals() else 'unknown'}")  # Debug info
        return ""

# Test function
if __name__ == "__main__":
    test_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "job_sites.xlsx")
    print(f"Testing with file: {test_path}")
    cv_text = get_cv_summary(test_path)
    print("\nCV Summary:")
    print("-" * 50)
    print(cv_text)
    print("-" * 50)
