from typing import Generator
import logging
import os

from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker
from sqlalchemy.engine import Engine

from ..core.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# Configure SQLite for better performance if using SQLite
@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    if settings.DATABASE_URL.startswith('sqlite'):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.close()

# Create database engine with appropriate configuration
def create_db_engine():
    """Create database engine with appropriate configuration based on database type"""
    logger.info(f"Connecting to database: {settings.DATABASE_URL}")

    # Default engine arguments
    engine_args = {
        "echo": settings.SQL_DEBUG,
    }

    # Add connection pooling for PostgreSQL
    if settings.DATABASE_URL.startswith('postgresql'):
        engine_args.update({
            "pool_pre_ping": True,
            "pool_size": 5,
            "max_overflow": 10,
        })

    # Create engine
    engine = create_engine(settings.DATABASE_URL, **engine_args)

    # Ensure SQLite database directory exists
    if settings.DATABASE_URL.startswith('sqlite'):
        db_path = settings.DATABASE_URL.replace('sqlite:///', '')
        if db_path and not db_path.startswith(':memory:'):
            os.makedirs(os.path.dirname(os.path.abspath(db_path)), exist_ok=True)

    return engine

# Create engine
engine = create_db_engine()

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db() -> Generator:
    """Dependency for getting database sessions"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()