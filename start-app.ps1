# JoMaDe Application Startup Script
# This script starts both the backend and frontend servers

Write-Host "Starting JoMaDe Application..." -ForegroundColor Cyan

# Start the backend server in a new window
Write-Host "Starting backend server..." -ForegroundColor Green
$backendDir = Join-Path -Path (Get-Location) -ChildPath "backend"
$venvPythonPath = Join-Path -Path $backendDir -ChildPath ".venv\Scripts\python.exe"

# Check if the Python virtual environment exists
if (-not (Test-Path $venvPythonPath)) {
    Write-Host "Python virtual environment not found. Creating one..." -ForegroundColor Yellow
    Push-Location $backendDir
    python -m venv .venv
    Pop-Location
}

# Start the backend server directly
Push-Location $backendDir
Start-Process $venvPythonPath -ArgumentList "-m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000" -WindowStyle Normal
Pop-Location

# Wait a moment for the backend to start
Write-Host "Waiting for backend server to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Start the frontend server in a new window
Write-Host "Starting frontend server..." -ForegroundColor Green
$frontendDir = Join-Path -Path (Get-Location) -ChildPath "frontend"

# Check if node_modules exists
$nodeModulesPath = Join-Path -Path $frontendDir -ChildPath "node_modules"
if (-not (Test-Path $nodeModulesPath)) {
    Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
    Push-Location $frontendDir
    npm install
    Pop-Location
}

# Check if .env.local exists
$envPath = Join-Path -Path $frontendDir -ChildPath ".env.local"
if (-not (Test-Path $envPath)) {
    Write-Host "Creating .env.local file..." -ForegroundColor Yellow
    @"
NEXT_PUBLIC_API_URL=http://localhost:8000
"@ | Out-File -FilePath $envPath -Encoding utf8
    Write-Host "Created .env.local file with default settings" -ForegroundColor Green
}

# Start the frontend server
Push-Location $frontendDir
Start-Process "npx" -ArgumentList "next dev --port 3000" -WindowStyle Normal
Pop-Location

# Wait a moment for the frontend to start
Write-Host "Waiting for frontend server to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Open the application in the browser
Write-Host "Opening application in browser..." -ForegroundColor Green
Start-Process "http://localhost:3000/simple"

Write-Host "JoMaDe application started successfully!" -ForegroundColor Cyan
Write-Host "Backend: http://localhost:8000" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:3000/simple" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to exit" -ForegroundColor Cyan

# Keep the script running
while ($true) {
    Start-Sleep -Seconds 1
}
