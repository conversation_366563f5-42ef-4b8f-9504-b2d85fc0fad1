# JoMaDe Frontend Startup Script
# This script starts the simplified JoMaDe frontend server

Write-Host "Starting JoMaDe Frontend Server..." -ForegroundColor Cyan

# Define paths
$frontendDir = Join-Path -Path (Get-Location) -ChildPath "frontend"

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "Found Node.js: $nodeVersion" -ForegroundColor Green
}
catch {
    Write-Host "Error: Node.js not found. Please install Node.js 14 or higher." -ForegroundColor Red
    exit 1
}

# Check if npm is installed
try {
    $npmVersion = npm --version
    Write-Host "Found npm: $npmVersion" -ForegroundColor Green
}
catch {
    Write-Host "Error: npm not found. Please install npm." -ForegroundColor Red
    exit 1
}

# Check if port 3000 is in use
$portInUse = $null -ne (Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue)
if ($portInUse) {
    Write-Host "Port 3000 is already in use. Attempting to stop the process..." -ForegroundColor Yellow
    $connections = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
    foreach ($conn in $connections) {
        if ($conn.State -eq 'Listen') {
            $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
            if ($null -ne $process) {
                Write-Host "Stopping process $($process.ProcessName) (ID: $($process.Id)) using port 3000" -ForegroundColor Yellow
                Stop-Process -Id $process.Id -Force
            }
        }
    }
    # Wait a moment for the port to be released
    Start-Sleep -Seconds 2
}

# Check if node_modules exists
$nodeModulesPath = Join-Path -Path $frontendDir -ChildPath "node_modules"
if (-not (Test-Path $nodeModulesPath)) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    Push-Location $frontendDir
    npm install
    Pop-Location
}

# Check if .env.local exists
$envPath = Join-Path -Path $frontendDir -ChildPath ".env.local"
if (-not (Test-Path $envPath)) {
    Write-Host "Creating .env.local file..." -ForegroundColor Yellow
    @"
NEXT_PUBLIC_API_URL=http://localhost:8000
"@ | Out-File -FilePath $envPath -Encoding utf8
    Write-Host "Created .env.local file with default settings" -ForegroundColor Green
}

# Start the frontend server
Write-Host "Starting frontend server..." -ForegroundColor Green
Push-Location $frontendDir
npx next dev --port 3000

# This script will continue running until the user presses Ctrl+C
