from fastapi import APIRouter, HTTPException, Depends, Body
from typing import Dict, Any
import os
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/v1/cv-md",
    tags=["cv-md"],
    responses={404: {"description": "Not found"}},
)

# Path to CV_Summary.md
CV_SUMMARY_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "CV_Summary.md")

@router.get("/summary")
async def get_cv_summary_md():
    """
    Get CV summary from CV_Summary.md
    """
    try:
        logger.info(f"Loading CV summary from: {CV_SUMMARY_PATH}")
        
        # Check if file exists
        if not os.path.exists(CV_SUMMARY_PATH):
            logger.error(f"File not found: {CV_SUMMARY_PATH}")
            raise HTTPException(status_code=404, detail="CV_Summary.md not found")
        
        # Read the CV summary from the file
        with open(CV_SUMMARY_PATH, 'r', encoding='utf-8') as file:
            cv_summary = file.read().strip()
        
        if not cv_summary:
            logger.warning("CV summary is empty")
            return {"summary": "", "message": "CV summary is empty"}
            
        logger.info(f"Successfully loaded CV summary ({len(cv_summary)} characters)")
        return {"summary": cv_summary}
        
    except Exception as e:
        logger.error(f"Error loading CV summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading CV summary: {str(e)}")

@router.post("/summary")
async def update_cv_summary_md(data: Dict[str, Any] = Body(...)):
    """
    Update CV summary in CV_Summary.md
    """
    try:
        summary = data.get("summary")
        if not summary:
            raise HTTPException(status_code=400, detail="CV summary is required")
        
        logger.info(f"Updating CV summary in: {CV_SUMMARY_PATH}")
        
        # Write the CV summary to the file
        with open(CV_SUMMARY_PATH, 'w', encoding='utf-8') as file:
            file.write(summary)
        
        logger.info(f"Successfully updated CV summary ({len(summary)} characters)")
        return {"message": "CV summary updated successfully"}
        
    except Exception as e:
        logger.error(f"Error updating CV summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating CV summary: {str(e)}")
