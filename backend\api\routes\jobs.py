from fastapi import APIRouter, HTTPException, Depends, Body
from typing import Dict, Any, List, Optional
import os
import sys
import pandas as pd
import importlib.util
from ..core.config import settings
import logging
import json

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix=f"{settings.API_V1_STR}/jobs",
    tags=["jobs"],
    responses={404: {"description": "Not found"}},
)

# Path to job_sites.xlsx
JOB_SITES_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))), "job_sites.xlsx")

# Add the processing directory to the Python path
PROCESSING_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "core", "processing")
if PROCESSING_DIR not in sys.path:
    sys.path.insert(0, PROCESSING_DIR)

# Global variable to store jobs
jobs_dict = {}

@router.get("/")
async def get_jobs():
    """
    Get job listings from job_sites.xlsx and Firecrawl
    """
    try:
        logger.info(f"Loading jobs from: {JOB_SITES_PATH}")

        # Check if file exists
        if not os.path.exists(JOB_SITES_PATH):
            logger.error(f"File not found: {JOB_SITES_PATH}")
            raise HTTPException(status_code=404, detail="job_sites.xlsx not found")

        # Import the job_importer module
        try:
            # Try to import from the processing directory
            from job_importer import import_jobs
        except ImportError:
            # If that fails, try to import using importlib
            spec = importlib.util.spec_from_file_location(
                "job_importer",
                os.path.join(PROCESSING_DIR, "job_importer.py")
            )
            job_importer = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(job_importer)
            import_jobs = job_importer.import_jobs

        # Import jobs
        job_titles = import_jobs(JOB_SITES_PATH)

        # Convert job titles to dictionary
        global jobs_dict
        jobs_dict = {}

        if job_titles:
            for job in job_titles:
                job_id, title = job.split(':', 1)
                jobs_dict[job_id.strip()] = {
                    'id': job_id.strip(),
                    'title': title.strip(),
                    'location': '',
                    'company': '',
                    'isShortlisted': False,
                    'score': 0
                }
            logger.info(f"Loaded {len(job_titles)} jobs from job_sites.xlsx")
        else:
            logger.warning("No jobs imported from job_sites.xlsx")

        # Try to get Firecrawl jobs if available
        try:
            # Import Firecrawl jobs from the firecrawl module
            from ..routes.firecrawl import firecrawl_jobs_dict

            if firecrawl_jobs_dict:
                # Add Firecrawl jobs to the main jobs dictionary
                for job_id, job_data in firecrawl_jobs_dict.items():
                    # Convert Firecrawl job format to match the main jobs dictionary
                    jobs_dict[job_id] = {
                        'id': job_id,
                        'title': job_data.get('title', ''),
                        'location': job_data.get('location', ''),
                        'company': job_data.get('company', ''),
                        'isShortlisted': job_data.get('isShortlisted', False),
                        'score': job_data.get('score', 0),
                        'description': job_data.get('description', ''),
                        'source': 'Firecrawl'
                    }
                logger.info(f"Added {len(firecrawl_jobs_dict)} jobs from Firecrawl")
        except Exception as e:
            logger.warning(f"Could not load Firecrawl jobs: {str(e)}")

        # If no jobs were found from any source, return empty list
        if not jobs_dict:
            logger.warning("No jobs found from any source")
            return []

        # Convert to list for API response
        jobs_list = [
            {
                'id': job_id,
                'title': job_data['title'],
                'location': job_data.get('location', ''),
                'company': job_data.get('company', ''),
                'isShortlisted': job_data.get('isShortlisted', False),
                'score': job_data.get('score', 0),
                'source': job_data.get('source', 'Excel')
            }
            for job_id, job_data in jobs_dict.items()
        ]

        logger.info(f"Successfully loaded {len(jobs_list)} jobs total")
        return jobs_list

    except Exception as e:
        logger.error(f"Error loading jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading jobs: {str(e)}")

@router.post("/shortlist")
async def shortlist_jobs(data: Dict[str, Any] = Body(...)):
    """
    Shortlist jobs based on CV summary
    """
    try:
        temperature = data.get("temperature", 0.7)
        threshold = data.get("threshold", 70)

        logger.info(f"Shortlisting jobs with temperature={temperature}, threshold={threshold}")

        # Check if we have jobs
        global jobs_dict
        if not jobs_dict:
            # Try to load jobs first
            await get_jobs()

            if not jobs_dict:
                logger.warning("No jobs to shortlist")
                raise HTTPException(status_code=400, detail="No jobs to shortlist")

        # Get CV summary
        try:
            from .cv import get_cv_summary
            cv_response = await get_cv_summary()
            cv_summary = cv_response.get("summary", "")
        except Exception as e:
            logger.error(f"Error getting CV summary: {str(e)}")
            raise HTTPException(status_code=500, detail="Error getting CV summary")

        if not cv_summary:
            logger.warning("CV summary is empty")
            raise HTTPException(status_code=400, detail="CV summary is empty")

        # Import the llm_shortlist module
        try:
            # Try to import from the processing directory
            from llm_shortlist import compare_cv_to_jobs
        except ImportError:
            # If that fails, try to import using importlib
            spec = importlib.util.spec_from_file_location(
                "llm_shortlist",
                os.path.join(PROCESSING_DIR, "llm_shortlist.py")
            )
            llm_shortlist = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(llm_shortlist)
            compare_cv_to_jobs = llm_shortlist.compare_cv_to_jobs

        # Shortlist jobs
        shortlisted = compare_cv_to_jobs(cv_summary, jobs_dict, temperature, threshold)

        if not shortlisted:
            logger.warning("No jobs were shortlisted")
            return {"shortlisted_count": 0, "shortlisted_jobs": []}

        # Update jobs_dict with shortlisted status
        shortlisted_ids = []
        for job_id, score in shortlisted:
            if job_id in jobs_dict:
                jobs_dict[job_id]["isShortlisted"] = True
                jobs_dict[job_id]["score"] = score
                shortlisted_ids.append(job_id)

        # Create response
        shortlisted_jobs = [
            {
                'id': job_id,
                'title': jobs_dict[job_id]['title'],
                'company': jobs_dict[job_id]['company'],
                'score': jobs_dict[job_id]['score'],
                'isShortlisted': True
            }
            for job_id in shortlisted_ids
        ]

        logger.info(f"Successfully shortlisted {len(shortlisted_jobs)} jobs")
        return {
            "shortlisted_count": len(shortlisted_jobs),
            "shortlisted_jobs": shortlisted_jobs
        }

    except Exception as e:
        logger.error(f"Error shortlisting jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error shortlisting jobs: {str(e)}")

@router.post("/rag-shortlist")
async def rag_shortlist_jobs(data: Dict[str, Any] = Body(...)):
    """
    Shortlist jobs using RAG (Retrieval Augmented Generation)
    """
    try:
        method = data.get("method", "faiss_preselect")  # 'faiss_preselect' or 'full_llm'
        faiss_threshold = data.get("faiss_threshold", 0.3)
        temperature = data.get("temperature", 0.7)

        logger.info(f"RAG shortlisting jobs with method={method}, faiss_threshold={faiss_threshold}, temperature={temperature}")

        # Check if we have jobs
        global jobs_dict
        if not jobs_dict:
            # Try to load jobs first
            await get_jobs()

            if not jobs_dict:
                logger.warning("No jobs to shortlist")
                raise HTTPException(status_code=400, detail="No jobs to shortlist")

        # Path to embeddings directory
        EMBEDDINGS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))), "data", "embeddings")

        # Check if embeddings exist
        faiss_files = [f for f in os.listdir(EMBEDDINGS_DIR) if f.endswith('.faiss')]
        if not faiss_files:
            logger.warning("No embeddings found")
            raise HTTPException(status_code=400, detail="No embeddings found. Please create embeddings first.")

        # Get the latest FAISS file
        latest_faiss = max(faiss_files, key=lambda f: os.path.getmtime(os.path.join(EMBEDDINGS_DIR, f)))
        faiss_path = os.path.join(EMBEDDINGS_DIR, latest_faiss)

        # Import the rag_shortlist module
        try:
            # Try to import from the processing directory
            from rag_shortlist import rag_shortlist
        except ImportError:
            # If that fails, try to import using importlib
            spec = importlib.util.spec_from_file_location(
                "rag_shortlist",
                os.path.join(PROCESSING_DIR, "rag_shortlist.py")
            )
            rag_shortlist_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(rag_shortlist_module)
            rag_shortlist = rag_shortlist_module.rag_shortlist

        # Shortlist jobs using RAG
        shortlisted = rag_shortlist(
            jobs_dict=jobs_dict,
            faiss_path=faiss_path,
            method=method,
            faiss_threshold=faiss_threshold,
            temperature=temperature
        )

        if not shortlisted:
            logger.warning("No jobs were shortlisted using RAG")
            return {"shortlisted_count": 0, "shortlisted_jobs": []}

        # Update jobs_dict with shortlisted status
        shortlisted_ids = []
        for job_id, score in shortlisted:
            if job_id in jobs_dict:
                jobs_dict[job_id]["isShortlisted"] = True
                jobs_dict[job_id]["score"] = score
                shortlisted_ids.append(job_id)

        # Create response
        shortlisted_jobs = [
            {
                'id': job_id,
                'title': jobs_dict[job_id]['title'],
                'company': jobs_dict[job_id]['company'],
                'score': jobs_dict[job_id]['score'],
                'isShortlisted': True
            }
            for job_id in shortlisted_ids
        ]

        logger.info(f"Successfully shortlisted {len(shortlisted_jobs)} jobs using RAG")
        return {
            "shortlisted_count": len(shortlisted_jobs),
            "shortlisted_jobs": shortlisted_jobs
        }

    except Exception as e:
        logger.error(f"Error RAG shortlisting jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error RAG shortlisting jobs: {str(e)}")

@router.post("/evaluate")
async def evaluate_jobs(data: Dict[str, Any] = Body(...)):
    """
    Evaluate shortlisted jobs
    """
    try:
        temperature = data.get("temperature", 0.7)
        job_ids = data.get("job_ids", [])  # Optional: specific job IDs to evaluate

        logger.info(f"Evaluating jobs with temperature={temperature}")

        # Check if we have jobs
        global jobs_dict
        if not jobs_dict:
            logger.warning("No jobs to evaluate")
            raise HTTPException(status_code=400, detail="No jobs to evaluate")

        # Filter shortlisted jobs
        if job_ids:
            # Use provided job IDs
            shortlisted_jobs = {
                job_id: jobs_dict[job_id]
                for job_id in job_ids
                if job_id in jobs_dict
            }
        else:
            # Use all shortlisted jobs
            shortlisted_jobs = {
                job_id: job_data
                for job_id, job_data in jobs_dict.items()
                if job_data.get("isShortlisted", False)
            }

        if not shortlisted_jobs:
            logger.warning("No shortlisted jobs to evaluate")
            raise HTTPException(status_code=400, detail="No shortlisted jobs to evaluate")

        # Import the llm_evaluation module
        try:
            # Try to import from the processing directory
            from llm_evaluation import evaluate_job_match
        except ImportError:
            # If that fails, try to import using importlib
            spec = importlib.util.spec_from_file_location(
                "llm_evaluation",
                os.path.join(PROCESSING_DIR, "llm_evaluation.py")
            )
            llm_evaluation = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(llm_evaluation)
            evaluate_job_match = llm_evaluation.evaluate_job_match

        # Evaluate jobs
        evaluations = evaluate_job_match(shortlisted_jobs, temperature)

        if not evaluations:
            logger.warning("No jobs were evaluated")
            return {"evaluated_count": 0, "evaluated_jobs": []}

        # Update jobs_dict with evaluation results
        for eval_result in evaluations:
            job_id = eval_result.get("prefixindex")
            if job_id in jobs_dict:
                jobs_dict[job_id]["evaluation"] = {
                    "fit_percentage": eval_result.get("fit_percentage", 0),
                    "pros": eval_result.get("pros", ""),
                    "cons": eval_result.get("cons", "")
                }

        # Create response
        evaluated_jobs = [
            {
                'id': job_id,
                'title': jobs_dict[job_id]['title'],
                'company': jobs_dict[job_id]['company'],
                'shortlist_score': jobs_dict[job_id]['score'],
                'fit_percentage': jobs_dict[job_id].get("evaluation", {}).get("fit_percentage", 0),
                'pros': jobs_dict[job_id].get("evaluation", {}).get("pros", ""),
                'cons': jobs_dict[job_id].get("evaluation", {}).get("cons", "")
            }
            for job_id in shortlisted_jobs.keys()
            if "evaluation" in jobs_dict[job_id]
        ]

        logger.info(f"Successfully evaluated {len(evaluated_jobs)} jobs")
        return {
            "evaluated_count": len(evaluated_jobs),
            "evaluated_jobs": evaluated_jobs
        }

    except Exception as e:
        logger.error(f"Error evaluating jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error evaluating jobs: {str(e)}")