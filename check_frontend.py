#!/usr/bin/env python
import os
import sys
import subprocess
from pathlib import Path

FRONTEND_DIR = Path(__file__).parent / 'frontend'

def check_next_installation():
    print("=== Frontend Installation Check ===\n")
    
    # Check if frontend directory exists
    if not FRONTEND_DIR.exists():
        print("❌ Frontend directory not found")
        return False
    
    # Check package.json
    if not (FRONTEND_DIR / 'package.json').exists():
        print("❌ package.json not found")
        return False
    
    # Check node_modules
    if not (FRONTEND_DIR / 'node_modules').exists():
        print("❌ node_modules not found. Dependencies may not be installed.")
        return False
    else:
        print("✓ node_modules found")
    
    # Check for Next.js installation in node_modules
    if not (FRONTEND_DIR / 'node_modules' / 'next').exists():
        print("❌ Next.js not found in node_modules. Try reinstalling.")
        return False
    else:
        print("✓ Next.js found in node_modules")
    
    # Check src directory
    if not (FRONTEND_DIR / 'src').exists():
        print("❌ src directory not found")
        return False
    else:
        print("✓ src directory found")
    
    # Check public directory
    if not (FRONTEND_DIR / 'public').exists():
        print("❌ public directory not found")
        return False
    else:
        print("✓ public directory found")
    
    print("\n✅ Frontend installation looks good")
    return True

if __name__ == "__main__":
    if check_next_installation():
        print("\nTrying to run frontend server directly...")
        try:
            npm_cmd = 'npm.cmd' if sys.platform == 'win32' else 'npm'
            subprocess.run(f'{npm_cmd} run dev', shell=True, cwd=FRONTEND_DIR)
        except Exception as e:
            print(f"Error: {e}")
    else:
        print("\nFrontend installation has issues. Try reinstalling dependencies:")
        print(f"cd {FRONTEND_DIR}")
        print("npm install") 