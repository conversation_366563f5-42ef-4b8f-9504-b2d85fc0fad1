/**
 * MCP (Message Control Protocol) Client for Chrome Browser Console Logs
 * This script captures browser console logs and sends them to the MCP server
 */

(function () {
  // Configuration
  const MCP_SERVER_URL = 'ws://localhost:8765';
  const RECONNECT_INTERVAL = 3000; // 3 seconds

  // WebSocket connection
  let socket = null;
  let isConnected = false;
  let reconnectTimer = null;

  // Connect to MCP server
  function connect() {
    if (socket) {
      socket.close();
    }

    try {
      socket = new WebSocket(MCP_SERVER_URL);

      socket.onopen = function () {
        console.log('[MCP] Connected to MCP server');
        isConnected = true;
        clearTimeout(reconnectTimer);

        // Send initial connection message
        sendLog({
          type: 'info',
          level: 'info',
          message: '<PERSON>rowser connected to MCP server',
          source: 'browser',
          url: window.location.href,
          userAgent: navigator.userAgent,
        });
      };

      socket.onclose = function () {
        console.log('[MCP] Disconnected from MCP server');
        isConnected = false;

        // Attempt to reconnect
        reconnectTimer = setTimeout(connect, RECONNECT_INTERVAL);
      };

      socket.onerror = function (error) {
        console.error('[MCP] WebSocket error:', error);
        isConnected = false;
      };
    } catch (error) {
      console.error('[MCP] Failed to connect to MCP server:', error);

      // Attempt to reconnect
      reconnectTimer = setTimeout(connect, RECONNECT_INTERVAL);
    }
  }

  // Send log to MCP server
  function sendLog(logData) {
    if (isConnected && socket) {
      try {
        socket.send(JSON.stringify(logData));
      } catch (error) {
        console.error('[MCP] Failed to send log:', error);
      }
    }
  }

  // Override console methods
  const originalConsole = {
    log: console.log,
    info: console.info,
    warn: console.warn,
    error: console.error,
    debug: console.debug,
  };

  // Override console.log
  console.log = function () {
    const args = Array.from(arguments);
    originalConsole.log.apply(console, args);

    sendLog({
      type: 'console',
      level: 'log',
      message: args.map(arg => formatArgument(arg)).join(' '),
      source: 'console.log',
    });
  };

  // Override console.info
  console.info = function () {
    const args = Array.from(arguments);
    originalConsole.info.apply(console, args);

    sendLog({
      type: 'console',
      level: 'info',
      message: args.map(arg => formatArgument(arg)).join(' '),
      source: 'console.info',
    });
  };

  // Override console.warn
  console.warn = function () {
    const args = Array.from(arguments);
    originalConsole.warn.apply(console, args);

    sendLog({
      type: 'console',
      level: 'warn',
      message: args.map(arg => formatArgument(arg)).join(' '),
      source: 'console.warn',
    });
  };

  // Override console.error
  console.error = function () {
    const args = Array.from(arguments);
    originalConsole.error.apply(console, args);

    sendLog({
      type: 'error',
      level: 'error',
      message: args.map(arg => formatArgument(arg)).join(' '),
      source: 'console.error',
    });
  };

  // Override console.debug
  console.debug = function () {
    const args = Array.from(arguments);
    originalConsole.debug.apply(console, args);

    sendLog({
      type: 'console',
      level: 'debug',
      message: args.map(arg => formatArgument(arg)).join(' '),
      source: 'console.debug',
    });
  };

  // Format argument for logging
  function formatArgument(arg) {
    if (arg === null) return 'null';
    if (arg === undefined) return 'undefined';

    if (typeof arg === 'object') {
      try {
        return JSON.stringify(arg);
      } catch (error) {
        return String(arg);
      }
    }

    return String(arg);
  }

  // Capture unhandled errors
  window.addEventListener('error', function (event) {
    sendLog({
      type: 'error',
      level: 'error',
      message: `Unhandled error: ${event.message} at ${event.filename}:${event.lineno}:${event.colno}`,
      source: 'window.onerror',
      stack: event.error ? event.error.stack : null,
    });
  });

  // Capture unhandled promise rejections
  window.addEventListener('unhandledrejection', function (event) {
    sendLog({
      type: 'error',
      level: 'error',
      message: `Unhandled promise rejection: ${formatArgument(event.reason)}`,
      source: 'unhandledrejection',
      stack: event.reason && event.reason.stack ? event.reason.stack : null,
    });
  });

  // Capture network errors
  const originalFetch = window.fetch;
  window.fetch = function () {
    const fetchArgs = arguments;
    const url = typeof fetchArgs[0] === 'string' ? fetchArgs[0] : fetchArgs[0].url;

    return originalFetch
      .apply(window, fetchArgs)
      .then(response => {
        if (!response.ok) {
          sendLog({
            type: 'network',
            level: response.status >= 500 ? 'error' : 'warn',
            message: `Fetch error: ${response.status} ${response.statusText} for ${url}`,
            source: 'fetch',
            status: response.status,
            statusText: response.statusText,
            url: url,
          });
        }
        return response;
      })
      .catch(error => {
        sendLog({
          type: 'network',
          level: 'error',
          message: `Fetch failed: ${error.message} for ${url}`,
          source: 'fetch',
          url: url,
        });
        throw error;
      });
  };

  // Capture XMLHttpRequest errors
  const originalXHROpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function () {
    const xhrArgs = arguments;
    const method = xhrArgs[0];
    const url = xhrArgs[1];

    this.addEventListener('load', function () {
      if (this.status >= 400) {
        sendLog({
          type: 'network',
          level: this.status >= 500 ? 'error' : 'warn',
          message: `XHR error: ${this.status} ${this.statusText} for ${method} ${url}`,
          source: 'xhr',
          status: this.status,
          statusText: this.statusText,
          method: method,
          url: url,
        });
      }
    });

    this.addEventListener('error', function () {
      sendLog({
        type: 'network',
        level: 'error',
        message: `XHR failed for ${method} ${url}`,
        source: 'xhr',
        method: method,
        url: url,
      });
    });

    return originalXHROpen.apply(this, xhrArgs);
  };

  // Connect to MCP server when the page loads
  if (document.readyState === 'complete') {
    connect();
  } else {
    window.addEventListener('load', connect);
  }

  // Reconnect when the page becomes visible
  document.addEventListener('visibilitychange', function () {
    if (document.visibilityState === 'visible' && !isConnected) {
      connect();
    }
  });

  // Log page navigation
  window.addEventListener('beforeunload', function () {
    sendLog({
      type: 'info',
      level: 'info',
      message: 'Page unloading',
      source: 'navigation',
      url: window.location.href,
    });
  });

  // Expose MCP client to window for debugging
  window.__MCP__ = {
    connect: connect,
    sendLog: sendLog,
    isConnected: function () {
      return isConnected;
    },
  };

  // Initial connection
  connect();

  console.log('[MCP] Console logging initialized');
})();
