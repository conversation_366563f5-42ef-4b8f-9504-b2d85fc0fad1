import openai # this function require OpenAi v. 0.28    
import os
from typing import List, Dict
import time
import re
from datetime import datetime

def compare_cv_to_jobs(cv_summary: str, jobs_dict: Dict, temperature: float = 0.7, threshold: int = 70) -> List[tuple]:
    """
    Compare CV summary to jobs using LLM.
    
    Args:
        cv_summary: The CV text
        jobs_dict: Dictionary containing job entries
        temperature: LLM temperature setting
        threshold: Minimum percentage match required (default: 70)
        
    Returns:
        List of tuples (job_id, percentage) for matches >= threshold
    """
    try:
        print("\n=== Starting Job Comparison ===")
        print(f"Processing {len(jobs_dict)} jobs")
        print(f"Using threshold: {threshold}%")
        
        # First reset all shortlist flags
        for job_data in jobs_dict.values():
            if 'shortlist' in job_data:
                job_data['shortlist']['is_shortlisted'] = False
                job_data['shortlist']['score'] = 0
        
        batch_size = 20
        jobs_list = list(jobs_dict.items())
        job_batches = [jobs_list[i:i + batch_size] for i in range(0, len(jobs_list), batch_size)]
        
        system_prompt = """You are a versatile HR professional used to work in an international environment, 
        especially in an English and German speaking setting. Your task is to shortlist job postings which fit 
        to a specific CV profile. Consider the candidate's education, industry experience, and seniority level.
        Return ONLY matching position IDs in format: A1,B2,C3 (no spaces, just commas)"""
        
        for batch_num, batch in enumerate(job_batches, 1):
            batch_text = "\n".join([f"{job_id}: {data['jobtitle']}" 
                                   for job_id, data in batch])
            
            try:
                # Minimize logging around the LLM call
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": f"""Kandidatenprofil:
{cv_summary}

Jobs:
{batch_text}

WICHTIG: 
- Bewerte jeden Job mit einer Prozentangabe (0-100%)
- Sei ehrlich und präzise in deiner Bewertung
- Nutze die volle Skala von 0-100%

Format: 'A1, 85%, A4, 30%'"""}
                    ],
                    temperature=temperature
                )
                
                # Process results
                result = response.choices[0].message.content.strip()
                parts = [p.strip() for p in result.split(',') if p.strip()]
                
                i = 0
                while i < len(parts):
                    try:
                        job_id_part = parts[i]
                        job_id_match = re.search(r'[A-Z]\d+', job_id_part)
                        if not job_id_match:
                            i += 1
                            continue
                        job_id = job_id_match.group()
                        
                        if i + 1 < len(parts):
                            percentage_part = parts[i + 1]
                            percentage_match = re.search(r'(\d+)', percentage_part)
                            if percentage_match:
                                percentage = int(percentage_match.group(1))
                                if percentage >= threshold and job_id in jobs_dict:
                                    print(f"\nUpdating {job_id} with shortlist score: {percentage}%")
                                    jobs_dict[job_id].update({
                                        'shortlist': {
                                            'is_shortlisted': True,
                                            'score': float(percentage),
                                            'timestamp': datetime.now().isoformat()
                                        },
                                        'display': {
                                            'color': 'green' if percentage >= threshold else 'red',
                                            'status': 'shortlisted'
                                        }
                                    })
                        i += 2
                    except Exception as e:
                        print(f"Error processing match: {str(e)}")
                        i += 1
                        continue
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                print(f"Error in batch {batch_num}: {str(e)}")
                continue
        
        # Create return list from jobs_dict in original order
        shortlisted_jobs = [(job_id, job_data['shortlist']['score']) 
                           for job_id, job_data in jobs_dict.items()
                           if job_data.get('shortlist', {}).get('is_shortlisted', False)]
        
        return shortlisted_jobs
        
    except Exception as e:
        print(f"Error in shortlisting: {str(e)}")
        return None

def update_shortlist(jobs_dict: Dict, shortlisted_ids: List[str]) -> Dict:
    """
    Update the jobs dictionary with shortlisted status
    
    Args:
        jobs_dict: Dictionary of all jobs
        shortlisted_ids: List of shortlisted job IDs
        
    Returns:
        Updated jobs dictionary
    """
    for job_id in jobs_dict:
        if job_id in shortlisted_ids:
            jobs_dict[job_id]["llm_sl"] = job_id
    return jobs_dict

# Test function
if __name__ == "__main__":
    # Test data
    cv_summary = """Business educated manager with 20 years experience in manufacturing industry.
    Held various leadership positions in sales, supply chain, and business development."""
    
    test_jobs = [
        "A1: CEO Manufacturing Company",
        "A2: Software Engineer",
        "A3: Head of Sales",
        "B1: Technical Service Manager",
        "B2: Geschäftsführer Industrieunternehmen",
        "B3: Vertriebsleiter DACH"
    ]
    
    print("Testing shortlist function...")
    shortlisted = compare_cv_to_jobs(cv_summary, test_jobs, batch_size=3)
    print("\nShortlisted positions:", shortlisted)
