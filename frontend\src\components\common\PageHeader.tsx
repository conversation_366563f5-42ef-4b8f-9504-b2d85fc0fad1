'use client';

import React, { ReactNode } from 'react';
import {
  Box,
  Flex,
  Heading,
  Text,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  useColorModeValue,
  HStack,
  Spacer,
} from '@chakra-ui/react';
import Link from 'next/link';
import { FiChevronRight } from 'react-icons/fi';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: BreadcrumbItem[];
  actions?: ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, subtitle, breadcrumbs, actions }) => {
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box mb={6}>
      {/* Breadcrumbs */}
      {breadcrumbs && breadcrumbs.length > 0 && (
        <Breadcrumb
          spacing="4px"
          separator={<FiChevronRight size="12px" color="gray.500" />}
          fontSize="xs"
          mb={2}
        >
          {breadcrumbs.map((crumb, index) => (
            <BreadcrumbItem key={index} isCurrentPage={index === breadcrumbs.length - 1}>
              {crumb.href ? (
                <BreadcrumbLink as={Link} href={crumb.href}>
                  {crumb.label}
                </BreadcrumbLink>
              ) : (
                <Text>{crumb.label}</Text>
              )}
            </BreadcrumbItem>
          ))}
        </Breadcrumb>
      )}

      {/* Header with title, subtitle and actions */}
      <Flex
        justify="space-between"
        align={{ base: 'flex-start', md: 'center' }}
        direction={{ base: 'column', md: 'row' }}
        pb={2}
        mb={3}
        borderBottom="1px"
        borderColor={borderColor}
      >
        <Box>
          <Heading as="h1" size="md" fontWeight="bold">
            {title}
          </Heading>
          {subtitle && (
            <Text mt={0.5} fontSize="sm" color="gray.500">
              {subtitle}
            </Text>
          )}
        </Box>

        {actions && (
          <HStack spacing={2} mt={{ base: 2, md: 0 }}>
            {actions}
          </HStack>
        )}
      </Flex>
    </Box>
  );
};

export default PageHeader;
