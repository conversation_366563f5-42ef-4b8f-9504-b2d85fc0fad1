from typing import List, Dict, Optional, Any
import httpx
import logging
import json
from fastapi import HTTPException
from datetime import datetime

from ..core.config import settings

logger = logging.getLogger(__name__)

class FirecrawlService:
    """Service for interacting with the Firecrawl API"""
    
    def __init__(self):
        """Initialize Firecrawl service with API configuration"""
        self.api_key = settings.FIRECRAWL_API_KEY
        self.api_url = "https://api.firecrawl.dev"  # Base URL for Firecrawl API
        self.headers = {
            "accept": "application/json",
            "x-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        logger.info(f"Initialized FirecrawlService")
        
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict:
        """Make a request to the Firecrawl API
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            **kwargs: Additional arguments to pass to httpx
            
        Returns:
            Dict: Response data
        """
        url = f"{self.api_url}{endpoint}"
        logger.debug(f"Making {method} request to {url}")
        
        try:
            async with httpx.AsyncClient() as client:
                logger.debug(f"Request headers: {self.headers}")
                logger.debug(f"Request kwargs: {kwargs}")
                
                response = await client.request(
                    method=method,
                    url=url,
                    headers=self.headers,
                    **kwargs
                )
                
                logger.debug(f"Response status: {response.status_code}")
                
                # Handle common error cases
                if response.status_code == 404:
                    raise HTTPException(status_code=404, detail="Resource not found")
                elif response.status_code == 401:
                    raise HTTPException(status_code=401, detail="Invalid API key")
                elif response.status_code == 403:
                    raise HTTPException(status_code=403, detail="Access forbidden")
                
                # Raise for other error status codes
                response.raise_for_status()
                
                # Return JSON response
                return response.json()
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e}")
            raise HTTPException(status_code=e.response.status_code, detail=str(e))
        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            raise HTTPException(status_code=500, detail=f"Request error: {str(e)}")
        except Exception as e:
            logger.error(f"Error making request: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def scrape_url(self, url: str) -> Dict:
        """Scrape a URL using Firecrawl
        
        Args:
            url: URL to scrape
            
        Returns:
            Dict: Scraped data
        """
        logger.info(f"Scraping URL with Firecrawl: {url}")
        
        try:
            # Create payload for scraping request
            payload = {
                "url": url,
                "extract_type": "job_posting",  # Specify that we're extracting job postings
                "options": {
                    "wait_for": ".job-listing, .job-post, .job-card, article, .vacancy",  # Common job listing selectors
                    "timeout": 30000  # 30 seconds timeout
                }
            }
            
            # Make request to Firecrawl API
            data = await self._make_request("POST", "/v1/extract", json=payload)
            
            logger.info(f"Successfully scraped URL: {url}")
            logger.debug(f"Scraped data: {data}")
            
            return data
        except Exception as e:
            logger.error(f"Error scraping URL {url}: {str(e)}")
            raise
    
    async def process_job_data(self, scraped_data: Dict, prefix: str) -> List[Dict]:
        """Process scraped job data and format it for storage
        
        Args:
            scraped_data: Raw data from Firecrawl API
            prefix: Three-letter prefix for job IDs
            
        Returns:
            List[Dict]: Processed job data
        """
        logger.info(f"Processing job data with prefix: {prefix}")
        
        try:
            # Extract job listings from scraped data
            job_listings = scraped_data.get("data", {}).get("job_postings", [])
            
            if not job_listings:
                logger.warning(f"No job listings found in scraped data")
                return []
            
            logger.info(f"Found {len(job_listings)} job listings")
            
            # Process each job listing
            processed_jobs = []
            for i, job in enumerate(job_listings, 1):
                job_id = f"{prefix}{i}"
                
                # Extract job details
                title = job.get("title", "Unknown Position")
                company = job.get("company", "")
                location = job.get("location", "")
                description = job.get("description", "")
                url = job.get("url", "")
                
                # Create processed job object
                processed_job = {
                    "id": job_id,
                    "title": title,
                    "company": company,
                    "location": location,
                    "description": description,
                    "url": url,
                    "source": "Firecrawl",
                    "scraped_at": datetime.utcnow().isoformat(),
                    "isShortlisted": False,
                    "score": 0
                }
                
                processed_jobs.append(processed_job)
            
            logger.info(f"Successfully processed {len(processed_jobs)} jobs")
            return processed_jobs
        except Exception as e:
            logger.error(f"Error processing job data: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing job data: {str(e)}")
