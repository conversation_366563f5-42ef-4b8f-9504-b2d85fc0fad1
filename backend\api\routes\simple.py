"""
Simple API routes for JoMaDe application.
This provides a simplified API structure that works with our new frontend.
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Body, Query, Depends
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create the router
router = APIRouter(prefix="/api", tags=["simple"])

# Data directory
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
os.makedirs(DATA_DIR, exist_ok=True)

# Data file paths
JOB_URLS_FILE = os.path.join(DATA_DIR, "job_urls.json")
CV_FILE = os.path.join(DATA_DIR, "cv.json")
JOBS_FILE = os.path.join(DATA_DIR, "jobs.json")

# Models
class JobSource(BaseModel):
    id: str
    url: str
    name: Optional[str] = None
    is_active: bool = True
    job_count: Optional[int] = 0
    created_at: str
    updated_at: Optional[str] = None
    prefix: Optional[str] = None

class CVSummary(BaseModel):
    summary: str
    skills: List[str] = []
    experience: List[str] = []
    education: List[str] = []

class Job(BaseModel):
    id: str
    title: str
    company: Optional[str] = None
    location: Optional[str] = None
    description: Optional[str] = None
    isShortlisted: Optional[bool] = False
    source: Optional[str] = None
    link: Optional[str] = None

# Helper functions
def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """Load data from a JSON file."""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # Create empty file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump([], f)
            return []
    except Exception as e:
        logger.error(f"Error loading data from {file_path}: {str(e)}")
        return []

def save_json_file(file_path: str, data: List[Dict[str, Any]]) -> bool:
    """Save data to a JSON file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving data to {file_path}: {str(e)}")
        return False

def generate_prefix(index: int) -> str:
    """Generate a three-letter prefix (AAA, AAB, AAC, etc.) from an index."""
    # Ensure index is positive
    index = max(0, index)
    
    # Convert to base-26 (A-Z) with 3 digits
    first_char = chr(65 + (index // 676) % 26)  # 26^2 = 676
    second_char = chr(65 + (index // 26) % 26)
    third_char = chr(65 + index % 26)
    
    return f"{first_char}{second_char}{third_char}"

# Initialize data files if they don't exist
def init_data_files():
    """Initialize data files if they don't exist."""
    # Job URLs
    if not os.path.exists(JOB_URLS_FILE):
        save_json_file(JOB_URLS_FILE, [])
    
    # CV
    if not os.path.exists(CV_FILE):
        save_json_file(CV_FILE, [])
    
    # Jobs
    if not os.path.exists(JOBS_FILE):
        save_json_file(JOBS_FILE, [])

# Initialize data files
init_data_files()

# Migrate data from existing markdown files if available
def migrate_from_markdown():
    """Migrate data from markdown files to JSON if needed."""
    try:
        # Check for job_url.md
        job_url_md_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "job_url.md")
        if os.path.exists(job_url_md_path) and len(load_json_file(JOB_URLS_FILE)) == 0:
            logger.info(f"Migrating data from {job_url_md_path}")
            with open(job_url_md_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Skip comment lines (first 10 lines)
            job_urls = []
            for i, line in enumerate(lines[10:], start=0):
                url = line.strip()
                if url and not url.startswith('#'):
                    prefix = generate_prefix(i)
                    job_urls.append({
                        "id": str(i + 1),
                        "url": url,
                        "name": f"Job Source {i + 1}",
                        "is_active": True,
                        "job_count": 0,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat(),
                        "prefix": prefix
                    })
            
            save_json_file(JOB_URLS_FILE, job_urls)
            logger.info(f"Migrated {len(job_urls)} URLs from markdown")
        
        # Check for CV_Summary.md
        cv_md_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "CV_Summary.md")
        if os.path.exists(cv_md_path) and len(load_json_file(CV_FILE)) == 0:
            logger.info(f"Migrating data from {cv_md_path}")
            with open(cv_md_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            cv_data = [{
                "summary": content,
                "skills": [],
                "experience": [],
                "education": []
            }]
            save_json_file(CV_FILE, cv_data)
            logger.info("Migrated CV summary from markdown")
            
    except Exception as e:
        logger.error(f"Error migrating data: {str(e)}")

# Try to migrate data on startup
migrate_from_markdown()

# Routes

# Job URLs endpoints
@router.get("/urls", response_model=List[JobSource])
async def get_urls():
    """Get all job URLs"""
    return load_json_file(JOB_URLS_FILE)

@router.post("/urls", response_model=JobSource)
async def add_url(url_data: Dict[str, Any] = Body(...)):
    """Add a new job URL"""
    if "url" not in url_data:
        raise HTTPException(status_code=400, detail="URL is required")
    
    job_urls = load_json_file(JOB_URLS_FILE)
    
    # Generate ID
    new_id = str(len(job_urls) + 1)
    
    # Generate prefix
    prefix = generate_prefix(len(job_urls))
    
    # Create new job URL
    new_url = {
        "id": new_id,
        "url": url_data["url"],
        "name": url_data.get("name", f"Job Source {new_id}"),
        "is_active": url_data.get("is_active", True),
        "job_count": 0,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "prefix": prefix
    }
    
    job_urls.append(new_url)
    save_json_file(JOB_URLS_FILE, job_urls)
    
    return new_url

@router.delete("/urls/{url_id}")
async def delete_url(url_id: str):
    """Delete a job URL"""
    job_urls = load_json_file(JOB_URLS_FILE)
    
    # Find the URL to delete
    for i, url in enumerate(job_urls):
        if url["id"] == url_id:
            job_urls.pop(i)
            save_json_file(JOB_URLS_FILE, job_urls)
            return {"message": f"URL with ID {url_id} deleted successfully"}
    
    raise HTTPException(status_code=404, detail=f"URL with ID {url_id} not found")

@router.put("/urls/{url_id}", response_model=JobSource)
async def update_url(url_id: str, url_data: Dict[str, Any] = Body(...)):
    """Update a job URL"""
    job_urls = load_json_file(JOB_URLS_FILE)
    
    # Find the URL to update
    for i, url in enumerate(job_urls):
        if url["id"] == url_id:
            # Update the URL
            job_urls[i].update(url_data)
            job_urls[i]["updated_at"] = datetime.now().isoformat()
            save_json_file(JOB_URLS_FILE, job_urls)
            return job_urls[i]
    
    raise HTTPException(status_code=404, detail=f"URL with ID {url_id} not found")

# CV endpoints
@router.get("/cv", response_model=CVSummary)
async def get_cv():
    """Get CV data"""
    cv_data = load_json_file(CV_FILE)
    if cv_data:
        return cv_data[0]
    return {"summary": "", "skills": [], "experience": [], "education": []}

@router.put("/cv", response_model=CVSummary)
async def update_cv(cv_data: Dict[str, Any] = Body(...)):
    """Update CV data"""
    if "summary" not in cv_data:
        raise HTTPException(status_code=400, detail="Summary is required")
    
    cv_entries = load_json_file(CV_FILE)
    
    if cv_entries:
        # Update existing entry
        cv_entries[0].update(cv_data)
        save_json_file(CV_FILE, cv_entries)
        return cv_entries[0]
    else:
        # Create new entry
        new_cv = {
            "summary": cv_data["summary"],
            "skills": cv_data.get("skills", []),
            "experience": cv_data.get("experience", []),
            "education": cv_data.get("education", [])
        }
        save_json_file(CV_FILE, [new_cv])
        return new_cv

# Jobs endpoints
@router.get("/jobs", response_model=List[Job])
async def get_jobs(
    source: Optional[str] = Query(None),
    shortlisted: Optional[bool] = Query(None)
):
    """
    Get jobs with optional filtering
    
    Args:
        source: Filter by source prefix (e.g., AAA)
        shortlisted: Filter by shortlisted status
    """
    jobs = load_json_file(JOBS_FILE)
    
    # Apply filters
    if source:
        jobs = [job for job in jobs if job.get("source") == source]
    
    if shortlisted is not None:
        jobs = [job for job in jobs if job.get("isShortlisted", False) == shortlisted]
    
    return jobs

@router.post("/jobs", response_model=Job)
async def add_job(job_data: Dict[str, Any] = Body(...)):
    """Add a new job"""
    if "title" not in job_data:
        raise HTTPException(status_code=400, detail="Title is required")
    
    jobs = load_json_file(JOBS_FILE)
    
    # Generate ID
    new_id = str(len(jobs) + 1)
    
    # Create new job
    new_job = {
        "id": new_id,
        "title": job_data["title"],
        "company": job_data.get("company"),
        "location": job_data.get("location"),
        "description": job_data.get("description"),
        "isShortlisted": job_data.get("isShortlisted", False),
        "source": job_data.get("source"),
        "link": job_data.get("link"),
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    jobs.append(new_job)
    save_json_file(JOBS_FILE, jobs)
    
    return new_job

@router.put("/jobs/{job_id}", response_model=Job)
async def update_job(job_id: str, job_data: Dict[str, Any] = Body(...)):
    """Update a job"""
    jobs = load_json_file(JOBS_FILE)
    
    # Find the job to update
    for i, job in enumerate(jobs):
        if job["id"] == job_id:
            # Update the job
            jobs[i].update(job_data)
            jobs[i]["updated_at"] = datetime.now().isoformat()
            save_json_file(JOBS_FILE, jobs)
            return jobs[i]
    
    raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")

@router.delete("/jobs/{job_id}")
async def delete_job(job_id: str):
    """Delete a job"""
    jobs = load_json_file(JOBS_FILE)
    
    # Find the job to delete
    for i, job in enumerate(jobs):
        if job["id"] == job_id:
            jobs.pop(i)
            save_json_file(JOBS_FILE, jobs)
            return {"message": f"Job with ID {job_id} deleted successfully"}
    
    raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")

# Scraping endpoint
@router.post("/scrape")
async def scrape_jobs():
    """
    Trigger job scraping
    
    This is a simplified version that just returns mock data.
    In a real implementation, this would call a scraper module.
    """
    # Get job URLs
    job_urls = load_json_file(JOB_URLS_FILE)
    
    # Create mock jobs
    jobs = load_json_file(JOBS_FILE)
    
    # Add 5 mock jobs per URL
    for url in job_urls:
        prefix = url.get("prefix", "AAA")
        for i in range(5):
            job_id = f"{prefix}{i+1}"
            
            # Check if job already exists
            if not any(job["id"] == job_id for job in jobs):
                jobs.append({
                    "id": job_id,
                    "title": f"Mock Job {job_id}",
                    "company": "Example Company",
                    "location": "Remote",
                    "description": f"This is a mock job for {url.get('url')}",
                    "isShortlisted": False,
                    "source": prefix,
                    "link": url.get("url"),
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                })
    
    # Save jobs
    save_json_file(JOBS_FILE, jobs)
    
    # Return result
    return {
        "success": True,
        "timestamp": datetime.now().isoformat(),
        "url_count": len(job_urls),
        "job_count": len(jobs),
        "message": "Jobs scraped successfully"
    }

# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "api_version": "0.2.0",
        "storage": {
            "job_urls": len(load_json_file(JOB_URLS_FILE)),
            "cv": len(load_json_file(CV_FILE)),
            "jobs": len(load_json_file(JOBS_FILE))
        }
    }
