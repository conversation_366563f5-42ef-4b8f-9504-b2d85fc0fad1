'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  Heading,
  Flex,
  Select,
  FormControl,
  FormLabel,
  useToast,
  Alert,
  AlertIcon,
  Badge,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Tooltip,
  HStack,
} from '@chakra-ui/react';
import { useEmbeddingStore } from '../stores/embeddingStore';

interface EmbeddingInfo {
  date: string;
  model: string;
  user: string;
  file_count: number;
  filename?: string;
}

interface EmbeddingManagerProps {
  onEmbeddingChange?: (embeddingInfo: EmbeddingInfo | null) => void;
  files?: File[];
  disabled?: boolean;
}

const EmbeddingManager: React.FC<EmbeddingManagerProps> = ({
  onEmbeddingChange,
  files = [],
  disabled = false,
}) => {
  // Use the embedding store
  const {
    embeddingInfo,
    isLoading,
    error,
    faissThreshold,
    setFaissThreshold,
    checkExistingEmbedding,
    createEmbedding,
    deleteEmbedding,
  } = useEmbeddingStore();

  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [selectedModel, setSelectedModel] = useState<string>('text-embedding-ada-002');
  const [showThresholdTooltip, setShowThresholdTooltip] = useState<boolean>(false);
  const toast = useToast();

  // Available embedding models
  const embeddingModels = [
    'text-embedding-ada-002',
    'text-embedding-3-small',
    'text-embedding-3-large',
  ];

  // Check for existing embedding on component mount
  useEffect(() => {
    const fetchEmbedding = async () => {
      const info = await checkExistingEmbedding();
      if (onEmbeddingChange) {
        onEmbeddingChange(info);
      }
    };

    fetchEmbedding();
  }, []);

  // Notify parent component when embedding info changes
  useEffect(() => {
    if (onEmbeddingChange) {
      onEmbeddingChange(embeddingInfo);
    }
  }, [embeddingInfo, onEmbeddingChange]);

  // Handle creating embedding
  const handleCreateEmbedding = async () => {
    if (files.length === 0) {
      toast({
        title: 'No Files Selected',
        description: 'Please upload files first to create an embedding',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      const result = await createEmbedding(files, selectedModel);

      if (result) {
        toast({
          title: 'Embedding Created',
          description: `Successfully created embedding with ${files.length} file(s)`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err) {
      toast({
        title: 'Embedding Failed',
        description: err instanceof Error ? err.message : 'Failed to create embedding',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Handle deleting embedding
  const handleDeleteEmbedding = async () => {
    if (!embeddingInfo) return;

    setIsDeleting(true);

    try {
      const success = await deleteEmbedding();

      if (success) {
        toast({
          title: 'Embedding Deleted',
          description: 'Successfully deleted embedding',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err) {
      toast({
        title: 'Deletion Failed',
        description: err instanceof Error ? err.message : 'Failed to delete embedding',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle RAG shortlisting
  const handleRagShortlist = () => {
    toast({
      title: 'RAG Shortlisting',
      description: 'Starting RAG-based shortlisting...',
      status: 'info',
      duration: 2000,
      isClosable: true,
    });
    // This would be connected to the shortlist function in a real implementation
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="white">
      <VStack align="stretch" spacing={4}>
        <Heading size="md">Embedding Management</Heading>

        {embeddingInfo ? (
          // Display current embedding info
          <Box>
            <Flex justify="space-between" align="center" mb={2}>
              <Text fontWeight="bold">Current Embedding:</Text>
              <Badge colorScheme="green">Active</Badge>
            </Flex>
            <Text>Date: {embeddingInfo.date}</Text>
            <Text>Model: {embeddingInfo.model}</Text>
            <Text>Files: {embeddingInfo.file_count}</Text>
            {embeddingInfo.filename && <Text>Filename: {embeddingInfo.filename}</Text>}

            <HStack mt={4}>
              <Button
                colorScheme="red"
                onClick={handleDeleteEmbedding}
                isLoading={isDeleting}
                leftIcon={<span>🗑️</span>}
              >
                Delete Embedding
              </Button>

              <Button colorScheme="blue" onClick={handleRagShortlist} leftIcon={<span>🔍</span>}>
                Shortlist with RAG
              </Button>
            </HStack>

            <Box mt={4}>
              <Text mb={2}>FAISS Threshold: {faissThreshold.toFixed(2)}</Text>
              <Slider
                id="faiss-threshold-slider"
                min={0}
                max={1}
                step={0.05}
                value={faissThreshold}
                onChange={val => setFaissThreshold(val)}
                onMouseEnter={() => setShowThresholdTooltip(true)}
                onMouseLeave={() => setShowThresholdTooltip(false)}
              >
                <SliderTrack>
                  <SliderFilledTrack />
                </SliderTrack>
                <Tooltip
                  hasArrow
                  bg="blue.500"
                  color="white"
                  placement="top"
                  isOpen={showThresholdTooltip}
                  label={`${faissThreshold.toFixed(2)}: ${
                    faissThreshold <= 0.2
                      ? 'More results (less strict)'
                      : faissThreshold >= 0.6
                        ? 'Fewer results (more strict)'
                        : 'Balanced'
                  }`}
                >
                  <SliderThumb />
                </Tooltip>
              </Slider>
            </Box>
          </Box>
        ) : (
          // Show embedding creation controls
          <Box>
            <FormControl mb={4}>
              <FormLabel>Select Embedding Model</FormLabel>
              <Select
                value={selectedModel}
                onChange={e => setSelectedModel(e.target.value)}
                isDisabled={isLoading || disabled}
              >
                {embeddingModels.map(model => (
                  <option key={model} value={model}>
                    {model}
                  </option>
                ))}
              </Select>
            </FormControl>

            <Button
              colorScheme="blue"
              onClick={handleCreateEmbedding}
              isLoading={isLoading}
              isDisabled={files.length === 0 || disabled}
              leftIcon={<span>📊</span>}
              width="full"
            >
              Create Embedding
            </Button>

            {files.length === 0 && (
              <Text mt={2} fontSize="sm" color="gray.500">
                Upload files first to create an embedding
              </Text>
            )}
          </Box>
        )}

        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}
      </VStack>
    </Box>
  );
};

export default EmbeddingManager;
