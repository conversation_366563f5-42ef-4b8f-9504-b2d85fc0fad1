from fastapi import APIRouter, HTTPException, Depends, Body
from typing import Dict, Any, List
import os
import pandas as pd
from ..core.config import settings
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix=f"{settings.API_V1_STR}/job-sources",
    tags=["job-sources"],
    responses={404: {"description": "Not found"}},
)

# Path to job_sites.xlsx from standardized settings
JOB_SITES_PATH = settings.JOB_SITES_EXCEL_PATH

@router.get("/")
async def get_job_sources():
    """
    Get job sources from job_sites.xlsx
    """
    try:
        logger.info(f"Loading job sources from: {JOB_SITES_PATH}")

        # Check if file exists
        if not os.path.exists(JOB_SITES_PATH):
            logger.error(f"File not found: {JOB_SITES_PATH}")
            raise HTTPException(status_code=404, detail="job_sites.xlsx not found")

        # Read the URL sheet from Excel
        df = pd.read_excel(JOB_SITES_PATH, sheet_name="URL")

        # Convert to list for API response
        sources = []
        for idx, row in df.iterrows():
            # Skip rows with NaN values in essential fields
            if pd.isna(row.get("URL")) or pd.isna(row.get("ScraperFunction")) or pd.isna(row.get("Data-File")):
                continue

            sources.append({
                "id": idx,
                "url": row.get("URL", ""),
                "scraper_function": row.get("ScraperFunction", ""),
                "data_file": row.get("Data-File", ""),
                "prefix_index": row.get("PrefixIndex", "")
            })

        logger.info(f"Successfully loaded {len(sources)} job sources")
        return sources

    except Exception as e:
        logger.error(f"Error loading job sources: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading job sources: {str(e)}")

@router.post("/")
async def add_job_source(data: Dict[str, Any] = Body(...)):
    """
    Add a new job source to job_sites.xlsx
    """
    try:
        url = data.get("url")
        source_type = data.get("source_type", "excel")

        if not url:
            raise HTTPException(status_code=400, detail="URL is required")

        logger.info(f"Adding job source: {url}")

        # Check if file exists
        if not os.path.exists(JOB_SITES_PATH):
            logger.error(f"File not found: {JOB_SITES_PATH}")
            raise HTTPException(status_code=404, detail="job_sites.xlsx not found")

        # Read the Excel file
        try:
            # Read the entire file
            xls = pd.ExcelFile(JOB_SITES_PATH)

            # Read all sheets
            sheets = {sheet_name: pd.read_excel(xls, sheet_name=sheet_name)
                     for sheet_name in xls.sheet_names}

            # Get the URL sheet
            url_sheet = sheets["URL"]

            # Determine the next available prefix index
            prefix_indices = url_sheet["PrefixIndex"].dropna().tolist()
            next_prefix = "A"
            if prefix_indices:
                # Find the highest prefix index
                highest_prefix = max(prefix_indices)
                # Increment the prefix
                next_prefix = chr(ord(highest_prefix[0]) + 1)

            # Determine the scraper function and data file based on source type
            scraper_function = "scrape_excel" if source_type == "excel" else "scrape_kadoa"
            data_file = f"scraped_urls/{url.split('/')[-1]}.xlsx"

            # Add new row to URL sheet
            new_row = pd.DataFrame({
                "URL": [url],
                "ScraperFunction": [scraper_function],
                "Data-File": [data_file],
                "PrefixIndex": [next_prefix]
            })

            url_sheet = pd.concat([url_sheet, new_row], ignore_index=True)
            sheets["URL"] = url_sheet

            # Create a writer to save the modified Excel file
            with pd.ExcelWriter(JOB_SITES_PATH) as writer:
                for sheet_name, sheet_data in sheets.items():
                    sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)

            logger.info(f"Successfully added job source: {url}")
            return {
                "message": "Job source added successfully",
                "url": url,
                "prefix_index": next_prefix
            }

        except Exception as e:
            logger.error(f"Error updating Excel file: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating Excel file: {str(e)}")

    except Exception as e:
        logger.error(f"Error adding job source: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding job source: {str(e)}")
