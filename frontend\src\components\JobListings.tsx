'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  Heading,
  Flex,
  List,
  ListItem,
  useToast,
  Alert,
  AlertIcon,
  Badge,
} from '@chakra-ui/react';

interface Job {
  id: string;
  title: string;
  company?: string;
  location?: string;
  isShortlisted?: boolean;
  score?: number;
}

interface JobListingsProps {
  onJobsLoaded?: (jobs: Job[]) => void;
}

const JobListings: React.FC<JobListingsProps> = ({ onJobsLoaded }) => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();

  // Notify parent component when jobs are loaded
  useEffect(() => {
    if (onJobsLoaded && jobs.length > 0) {
      onJobsLoaded(jobs);
    }
  }, [jobs, onJobsLoaded]);

  // Fetch job listings from backend
  const fetchJobs = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:8000/api/v1/jobs');

      if (!response.ok) {
        throw new Error(`Failed to fetch jobs: ${response.status}`);
      }

      const data = await response.json();
      setJobs(data);

      toast({
        title: 'Jobs Loaded',
        description: `Successfully loaded ${data.length} jobs`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      if (onJobsLoaded) {
        onJobsLoaded(data);
      }
    } catch (err) {
      console.error('Error fetching jobs:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch jobs');

      toast({
        title: 'Fetch Failed',
        description: err instanceof Error ? err.message : 'Failed to fetch jobs',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="white">
      <VStack align="stretch" spacing={4}>
        <Flex justify="space-between" align="center">
          <Heading size="md">Job Listings</Heading>
          <Text>Count: {jobs.length}</Text>
        </Flex>

        <Button
          colorScheme="blue"
          onClick={fetchJobs}
          isLoading={isLoading}
          leftIcon={<span>🔍</span>}
        >
          Scrape Job Postings
        </Button>

        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}

        {jobs.length > 0 ? (
          <List spacing={2}>
            {jobs.map(job => (
              <ListItem key={job.id} p={2} borderWidth="1px" borderRadius="md">
                <Flex justify="space-between" align="center">
                  <Box>
                    <Text fontWeight="bold">{job.title}</Text>
                    {job.company && <Text fontSize="sm">{job.company}</Text>}
                    {job.location && (
                      <Text fontSize="xs" color="gray.600">
                        {job.location}
                      </Text>
                    )}
                  </Box>
                  {job.isShortlisted && (
                    <Badge colorScheme="green">
                      {job.score ? `Match: ${job.score}%` : 'Shortlisted'}
                    </Badge>
                  )}
                </Flex>
              </ListItem>
            ))}
          </List>
        ) : (
          <Text color="gray.500">No jobs found. Click "Scrape Job Postings" to load jobs.</Text>
        )}
      </VStack>
    </Box>
  );
};

export default JobListings;
