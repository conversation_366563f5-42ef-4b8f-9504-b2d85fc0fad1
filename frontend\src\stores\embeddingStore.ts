import { create } from 'zustand';

interface EmbeddingInfo {
  date: string;
  model: string;
  user: string;
  file_count: number;
  filename?: string;
}

interface EmbeddingState {
  embeddingInfo: EmbeddingInfo | null;
  isLoading: boolean;
  error: string | null;
  faissThreshold: number;

  // Actions
  setEmbeddingInfo: (info: EmbeddingInfo | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setFaissThreshold: (threshold: number) => void;

  // API calls
  checkExistingEmbedding: () => Promise<EmbeddingInfo | null>;
  createEmbedding: (files: File[], model: string) => Promise<EmbeddingInfo | null>;
  deleteEmbedding: () => Promise<boolean>;
}

export const useEmbeddingStore = create<EmbeddingState>((set, get) => ({
  embeddingInfo: null,
  isLoading: false,
  error: null,
  faissThreshold: 0.3,

  // Actions
  setEmbeddingInfo: info => set({ embeddingInfo: info }),
  setLoading: loading => set({ isLoading: loading }),
  setError: error => set({ error }),
  setFaissThreshold: threshold => set({ faissThreshold: threshold }),

  // API calls
  checkExistingEmbedding: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await fetch('http://localhost:8000/api/v1/embeddings/info');

      if (!response.ok) {
        if (response.status === 404) {
          // No embedding found, not an error
          set({ embeddingInfo: null, isLoading: false });
          return null;
        }
        throw new Error(`Failed to check embedding: ${response.status}`);
      }

      const data = await response.json();
      set({ embeddingInfo: data, isLoading: false });
      return data;
    } catch (err) {
      console.error('Error checking embedding:', err);
      set({
        error: err instanceof Error ? err.message : 'Failed to check embedding',
        isLoading: false,
      });
      return null;
    }
  },

  createEmbedding: async (files, model) => {
    set({ isLoading: true, error: null });

    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });
      formData.append('model', model);

      const response = await fetch('http://localhost:8000/api/v1/embeddings/create', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to create embedding: ${response.status}`);
      }

      const data = await response.json();
      set({ embeddingInfo: data, isLoading: false });
      return data;
    } catch (err) {
      console.error('Error creating embedding:', err);
      set({
        error: err instanceof Error ? err.message : 'Failed to create embedding',
        isLoading: false,
      });
      return null;
    }
  },

  deleteEmbedding: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await fetch('http://localhost:8000/api/v1/embeddings/delete', {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete embedding: ${response.status}`);
      }

      set({ embeddingInfo: null, isLoading: false });
      return true;
    } catch (err) {
      console.error('Error deleting embedding:', err);
      set({
        error: err instanceof Error ? err.message : 'Failed to delete embedding',
        isLoading: false,
      });
      return false;
    }
  },
}));
