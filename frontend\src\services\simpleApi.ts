/**
 * Simplified API client for JoMaDe application.
 * This replaces the complex API structure with a simpler approach.
 */

import { JobSource, Job, CVSummary } from '../types';

// API base URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Fallback data for when the API is not available
const FALLBACK_JOB_URLS: JobSource[] = [
  {
    id: '1',
    url: 'https://careers.eoexecutives.com/',
    name: 'Job Source 1',
    is_active: true,
    job_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    prefix: 'AAA',
  },
  {
    id: '2',
    url: 'https://mbmanagement.de/category/aktuelle-stellenangebote',
    name: 'Job Source 2',
    is_active: true,
    job_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    prefix: 'AAB',
  },
  {
    id: '3',
    url: 'https://vakanzen.drmaier-partner.de/stellenanzeigen/',
    name: 'Job Source 3',
    is_active: true,
    job_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    prefix: 'AAC',
  },
];

const FALLBACK_CV_SUMMARY: CVSummary = {
  summary: 'Senior executive with over 20 years of experience in the manufacturing and industrial sectors, specializing in supply chain management, business development, and sales.',
  skills: ['Supply Chain Management', 'Business Development', 'Sales'],
  experience: ['20+ years in manufacturing and industrial sectors'],
  education: ['MBA in General Management'],
};

/**
 * Simple API client with built-in fallbacks
 */
export const simpleApi = {
  /**
   * Get all job URLs
   */
  async getJobUrls(): Promise<JobSource[]> {
    try {
      // Try the simplified API first
      const response = await fetch(`${API_URL}/api/urls`);

      if (!response.ok) {
        // If that fails, try the original API
        const originalResponse = await fetch(`${API_URL}/api/v1/job-url-md`);

        if (!originalResponse.ok) {
          throw new Error(`Failed to fetch job URLs: ${originalResponse.status}`);
        }

        return await originalResponse.json();
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching job URLs:', error);
      console.log('Using fallback job URLs');
      return FALLBACK_JOB_URLS;
    }
  },

  /**
   * Add a new job URL
   */
  async addJobUrl(url: string, name?: string): Promise<JobSource> {
    try {
      const response = await fetch(`${API_URL}/api/urls`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, name }),
      });

      if (!response.ok) {
        throw new Error(`Failed to add job URL: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error adding job URL:', error);
      throw error;
    }
  },

  /**
   * Delete a job URL
   */
  async deleteJobUrl(id: string): Promise<void> {
    try {
      const response = await fetch(`${API_URL}/api/urls/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete job URL: ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting job URL:', error);
      throw error;
    }
  },

  /**
   * Update a job URL
   */
  async updateJobUrl(id: string, data: Partial<JobSource>): Promise<JobSource> {
    try {
      const response = await fetch(`${API_URL}/api/urls/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to update job URL: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating job URL:', error);
      throw error;
    }
  },

  /**
   * Get CV summary
   */
  async getCVSummary(): Promise<CVSummary> {
    try {
      const response = await fetch(`${API_URL}/api/cv`);

      if (!response.ok) {
        throw new Error(`Failed to fetch CV summary: ${response.status}`);
      }

      const data = await response.json();
      return {
        summary: data.summary || '',
        skills: data.skills || [],
        experience: data.experience || [],
        education: data.education || [],
      };
    } catch (error) {
      console.error('Error fetching CV summary:', error);
      console.log('Using fallback CV summary');
      return FALLBACK_CV_SUMMARY;
    }
  },

  /**
   * Update CV summary
   */
  async updateCVSummary(summary: string): Promise<CVSummary> {
    try {
      const response = await fetch(`${API_URL}/api/cv`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ summary }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update CV summary: ${response.status}`);
      }

      const data = await response.json();
      return {
        summary: data.summary || '',
        skills: data.skills || [],
        experience: data.experience || [],
        education: data.education || [],
      };
    } catch (error) {
      console.error('Error updating CV summary:', error);
      throw error;
    }
  },

  /**
   * Get jobs
   */
  async getJobs(source?: string, shortlisted?: boolean): Promise<Job[]> {
    try {
      let url = `${API_URL}/api/jobs`;
      const params = new URLSearchParams();

      if (source) {
        params.append('source', source);
      }

      if (shortlisted !== undefined) {
        params.append('shortlisted', shortlisted.toString());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch jobs: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching jobs:', error);
      return [];
    }
  },

  /**
   * Trigger job scraping
   */
  async scrapeJobs(): Promise<{ success: boolean; job_count: number; url_count: number; message: string }> {
    try {
      // Try the simplified API first
      const response = await fetch(`${API_URL}/api/scrape`, {
        method: 'POST',
      });

      if (!response.ok) {
        // If that fails, try the original API
        const originalResponse = await fetch(`${API_URL}/api/v1/firecrawl/scrape`, {
          method: 'POST',
        });

        if (!originalResponse.ok) {
          throw new Error(`Failed to scrape jobs: ${originalResponse.status}`);
        }

        return await originalResponse.json();
      }

      return await response.json();
    } catch (error) {
      console.error('Error scraping jobs:', error);

      // Return mock data as fallback
      return {
        success: true,
        job_count: 15,
        url_count: 3,
        message: 'Using mock data since the backend is not available',
      };
    }
  },

  /**
   * Check API health
   */
  async checkHealth(): Promise<{ status: string }> {
    try {
      const response = await fetch(`${API_URL}/health`);

      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Health check failed:', error);
      return { status: 'unhealthy' };
    }
  },
};

export default simpleApi;
