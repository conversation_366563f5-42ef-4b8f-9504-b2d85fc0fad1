from fastapi import APIRouter, HTTPException, Body
from typing import Dict, Any, List
import os
import logging
from ..core.config import settings
from ..services.firecrawl import FirecrawlService

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix=f"{settings.API_V1_STR}/firecrawl",
    tags=["firecrawl"],
    responses={404: {"description": "Not found"}},
)

# Path to job_url.md
JOB_URL_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "job_url.md")

# Global variable to store scraped jobs
firecrawl_jobs_dict = {}

@router.get("/jobs")
async def get_firecrawl_jobs():
    """
    Get all jobs scraped with Firecrawl
    """
    try:
        logger.info("Getting Firecrawl jobs")
        
        # Check if we have any jobs
        global firecrawl_jobs_dict
        if not firecrawl_jobs_dict:
            logger.warning("No Firecrawl jobs found")
            return []
        
        # Convert to list for API response
        jobs_list = list(firecrawl_jobs_dict.values())
        
        logger.info(f"Returning {len(jobs_list)} Firecrawl jobs")
        return jobs_list
    except Exception as e:
        logger.error(f"Error getting Firecrawl jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting Firecrawl jobs: {str(e)}")

@router.post("/scrape")
async def scrape_job_urls():
    """
    Scrape job URLs from job_url.md using Firecrawl
    """
    try:
        logger.info(f"Scraping job URLs from: {JOB_URL_PATH}")
        
        # Check if file exists
        if not os.path.exists(JOB_URL_PATH):
            logger.error(f"File not found: {JOB_URL_PATH}")
            raise HTTPException(status_code=404, detail="job_url.md not found")
        
        # Read the job URLs from the file
        with open(JOB_URL_PATH, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        # Filter out comments and empty lines
        urls = [line.strip() for line in lines if line.strip() and not line.strip().startswith('#')]
        
        if not urls:
            logger.warning("No URLs found in job_url.md")
            raise HTTPException(status_code=400, detail="No URLs found in job_url.md")
        
        logger.info(f"Found {len(urls)} URLs to scrape")
        
        # Initialize Firecrawl service
        firecrawl = FirecrawlService()
        
        # Global variable to store jobs
        global firecrawl_jobs_dict
        firecrawl_jobs_dict = {}
        
        # Scrape each URL
        all_jobs = []
        for idx, url in enumerate(urls):
            try:
                # Generate prefix for this URL
                prefix = generate_prefix_from_id(idx)
                
                logger.info(f"Scraping URL {idx+1}/{len(urls)}: {url} with prefix {prefix}")
                
                # Scrape URL
                scraped_data = await firecrawl.scrape_url(url)
                
                # Process job data
                jobs = await firecrawl.process_job_data(scraped_data, prefix)
                
                if jobs:
                    # Add jobs to global dictionary
                    for job in jobs:
                        job_id = job["id"]
                        firecrawl_jobs_dict[job_id] = job
                    
                    all_jobs.extend(jobs)
                    logger.info(f"Added {len(jobs)} jobs from {url}")
                else:
                    logger.warning(f"No jobs found at {url}")
            except Exception as e:
                logger.error(f"Error scraping URL {url}: {str(e)}")
                # Continue with next URL
                continue
        
        logger.info(f"Successfully scraped {len(all_jobs)} jobs from {len(urls)} URLs")
        
        return {
            "message": f"Successfully scraped {len(all_jobs)} jobs from {len(urls)} URLs",
            "job_count": len(all_jobs),
            "url_count": len(urls)
        }
    except Exception as e:
        logger.error(f"Error scraping job URLs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error scraping job URLs: {str(e)}")

@router.post("/scrape-url")
async def scrape_single_url(data: Dict[str, Any] = Body(...)):
    """
    Scrape a single URL using Firecrawl
    """
    try:
        url = data.get("url")
        prefix = data.get("prefix")
        
        if not url:
            raise HTTPException(status_code=400, detail="URL is required")
        
        if not prefix:
            # Generate a random prefix if not provided
            prefix = "FCR"  # Firecrawl prefix
        
        logger.info(f"Scraping single URL: {url} with prefix {prefix}")
        
        # Initialize Firecrawl service
        firecrawl = FirecrawlService()
        
        # Scrape URL
        scraped_data = await firecrawl.scrape_url(url)
        
        # Process job data
        jobs = await firecrawl.process_job_data(scraped_data, prefix)
        
        if not jobs:
            logger.warning(f"No jobs found at {url}")
            return {"message": "No jobs found", "job_count": 0}
        
        # Add jobs to global dictionary
        global firecrawl_jobs_dict
        for job in jobs:
            job_id = job["id"]
            firecrawl_jobs_dict[job_id] = job
        
        logger.info(f"Successfully scraped {len(jobs)} jobs from {url}")
        
        return {
            "message": f"Successfully scraped {len(jobs)} jobs",
            "job_count": len(jobs),
            "jobs": jobs
        }
    except Exception as e:
        logger.error(f"Error scraping URL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error scraping URL: {str(e)}")

def generate_prefix_from_id(index: int) -> str:
    """
    Generate a three-letter prefix from an index (AAA, AAB, AAC, etc.)
    
    Args:
        index: Zero-based index
        
    Returns:
        str: Three-letter prefix
    """
    letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    first = letters[index // (26 * 26) % 26]
    second = letters[(index // 26) % 26]
    third = letters[index % 26]
    return f"{first}{second}{third}"
