'use client';

import React from 'react';
import {
  Box,
  Grid,
  GridItem,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Text,
  Flex,
  Icon,
  useColorModeValue,
} from '@chakra-ui/react';
import { FiBriefcase, FiCheckCircle, FiStar, FiTrendingUp } from 'react-icons/fi';
import { MainLayout } from '../../components/layout';
import { Card, PageHeader } from '../../components/common';

const DashboardPage: React.FC = () => {
  // Sample data
  const stats = [
    {
      label: 'Total Jobs',
      value: 256,
      icon: FiBriefcase,
      change: 12,
      color: 'blue',
    },
    {
      label: 'Shortlisted',
      value: 42,
      icon: FiCheckCircle,
      change: 8,
      color: 'green',
    },
    {
      label: 'Top Matches',
      value: 15,
      icon: FiStar,
      change: 3,
      color: 'orange',
    },
    {
      label: 'New Today',
      value: 28,
      icon: FiTrendingUp,
      change: -5,
      color: 'purple',
    },
  ];

  return (
    <MainLayout title="Dashboard">
      <PageHeader
        title="Dashboard"
        subtitle="Overview of your job matching status"
        breadcrumbs={[{ label: 'Home', href: '/' }, { label: 'Dashboard' }]}
      />

      {/* Stats Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
        {stats.map((stat, index) => (
          <Card key={index} variant="elevated">
            <Flex align="center">
              <Box
                p={3}
                borderRadius="full"
                bg={useColorModeValue(`${stat.color}.100`, `${stat.color}.900`)}
                color={useColorModeValue(`${stat.color}.500`, `${stat.color}.200`)}
                mr={4}
              >
                <Icon as={stat.icon} boxSize={6} />
              </Box>
              <Stat>
                <StatLabel>{stat.label}</StatLabel>
                <StatNumber>{stat.value}</StatNumber>
                <StatHelpText>
                  <StatArrow type={stat.change >= 0 ? 'increase' : 'decrease'} />
                  {Math.abs(stat.change)}% from last week
                </StatHelpText>
              </Stat>
            </Flex>
          </Card>
        ))}
      </SimpleGrid>

      {/* Main Content Grid */}
      <Grid templateColumns="repeat(12, 1fr)" gap={6}>
        {/* Recent Activity */}
        <GridItem colSpan={{ base: 12, lg: 8 }}>
          <Card
            title="Recent Activity"
            subtitle="Latest updates from your job search"
            minHeight="300px"
          >
            <Text>Recent activity content will go here</Text>
          </Card>
        </GridItem>

        {/* Quick Actions */}
        <GridItem colSpan={{ base: 12, lg: 4 }}>
          <Card title="Quick Actions" subtitle="Common tasks" minHeight="300px">
            <Text>Quick actions will go here</Text>
          </Card>
        </GridItem>

        {/* Job Matching */}
        <GridItem colSpan={{ base: 12, lg: 6 }}>
          <Card title="Job Matching" subtitle="Match score distribution" minHeight="300px">
            <Text>Job matching chart will go here</Text>
          </Card>
        </GridItem>

        {/* Skill Analysis */}
        <GridItem colSpan={{ base: 12, lg: 6 }}>
          <Card title="Skill Analysis" subtitle="Top skills in demand" minHeight="300px">
            <Text>Skill analysis chart will go here</Text>
          </Card>
        </GridItem>
      </Grid>
    </MainLayout>
  );
};

export default DashboardPage;
