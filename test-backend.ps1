# Test Backend Server
# This script starts only the backend server for testing

# Define paths
$rootDir = Get-Location
$backendDir = Join-Path -Path $rootDir -ChildPath 'backend'

Write-Host '===== Starting Backend Server for Testing =====' -ForegroundColor Cyan
Write-Host ''

# Define virtual environment paths
$venvPath = Join-Path -Path $backendDir -ChildPath '.venv'
$venvPythonPath = Join-Path -Path $venvPath -ChildPath 'Scripts\python.exe'

# Verify Python exists in virtual environment
if (-not (Test-Path $venvPythonPath)) {
    Write-Host 'Error: Python not found in virtual environment' -ForegroundColor Red
    Write-Host 'Try recreating the virtual environment:' -ForegroundColor Yellow
    Write-Host '1. Delete the .venv folder in the backend directory' -ForegroundColor Yellow
    Write-Host '2. Run the full run.ps1 script again' -ForegroundColor Yellow
    exit 1
}

# Function to check if a port is in use
function Test-PortInUse {
    param (
        [int]$Port
    )

    $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    return ($null -ne $connections)
}

# Function to kill process using a specific port
function Stop-ProcessUsingPort {
    param (
        [int]$Port
    )

    $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($null -ne $connections) {
        foreach ($conn in $connections) {
            $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
            if ($null -ne $process) {
                Write-Host "Stopping process $($process.ProcessName) (ID: $($process.Id)) using port $Port" -ForegroundColor Yellow
                Stop-Process -Id $process.Id -Force
                return $true
            }
        }
    }
    return $false
}

# Start backend server
try {
    Write-Host 'Starting FastAPI backend server...' -ForegroundColor Yellow

    # Check if port 8000 is already in use
    if (Test-PortInUse -Port 8000) {
        Write-Host "Port 8000 is already in use. Attempting to stop the process..." -ForegroundColor Yellow
        if (Stop-ProcessUsingPort -Port 8000) {
            Write-Host "Successfully stopped process using port 8000" -ForegroundColor Green
            # Wait a moment for the port to be released
            Start-Sleep -Seconds 2
        } else {
            Write-Host "Could not stop process using port 8000. Using port 8001 instead." -ForegroundColor Yellow
            $backendPort = 8001
        }
    } else {
        $backendPort = 8000
    }

    # Start the backend server with more detailed logging
    Write-Host "Starting backend server with command: $venvPythonPath -m uvicorn api.main:app --reload --host 0.0.0.0 --port $backendPort --log-level debug" -ForegroundColor Yellow
    Write-Host "Working directory: $backendDir" -ForegroundColor Yellow

    # Run the command directly in the current window
    Push-Location $backendDir
    & "$venvPythonPath" -m uvicorn api.main:app --reload --host 0.0.0.0 --port "$backendPort" --log-level debug
    Pop-Location
}
catch {
    Write-Host "Error starting backend server: $_" -ForegroundColor Red
    exit 1
}
