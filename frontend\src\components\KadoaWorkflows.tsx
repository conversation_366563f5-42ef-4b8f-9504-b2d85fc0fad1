import React, { useEffect, useState } from 'react';
import {
  Box,
  VStack,
  Text,
  List,
  ListItem,
  Link,
  Badge,
  Heading,
  Flex,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Switch,
  FormControl,
  FormLabel,
  Button,
  Input,
  InputGroup,
  InputRightElement,
} from '@chakra-ui/react';
import { useKadoaStore } from '../stores/kadoaStore';

interface Workflow {
  id: string;
  url: string;
  state: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'PAUSED' | 'ACTIVE' | 'PREVIEW';
  job_count?: number;
}

const KadoaWorkflows: React.FC = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const [isKadoaEnabled, setIsKadoaEnabled] = useState(false);
  const [newWorkflowUrl, setNewWorkflowUrl] = useState('');

  // Only fetch workflows when Kadoa is enabled
  useEffect(() => {
    if (isKadoaEnabled) {
      fetchWorkflows();
    } else {
      // Reset state when Kadoa is disabled
      setWorkflows([]);
      setError(null);
      setErrorDetails(null);
      setIsLoading(false);
    }
  }, [isKadoaEnabled]);

  const fetchWorkflows = async () => {
    if (!isKadoaEnabled) return;

    setIsLoading(true);
    setError(null);
    setErrorDetails(null);

    try {
      console.log('Fetching workflows...');
      const response = await fetch('http://localhost:8000/api/v1/kadoa/workflows');
      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('Received data:', data);
      setWorkflows(data);
    } catch (err) {
      console.error('Error in fetchWorkflows:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch workflows';
      const errorStack = err instanceof Error ? err.stack || null : null;
      setError(errorMessage);
      setErrorDetails(errorStack);
    } finally {
      setIsLoading(false);
    }
  };

  const getStateBadgeColor = (state: Workflow['state']) => {
    const colorMap = {
      ACTIVE: 'green',
      PAUSED: 'yellow',
      PENDING: 'blue',
      RUNNING: 'blue',
      COMPLETED: 'green',
      FAILED: 'red',
      PREVIEW: 'purple',
    };
    return colorMap[state] || 'gray';
  };

  // Function to add a new workflow
  const addWorkflow = async () => {
    if (!newWorkflowUrl || !isKadoaEnabled) return;

    try {
      setIsLoading(true);
      const response = await fetch('http://localhost:8000/api/v1/kadoa/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: newWorkflowUrl,
          schema_type: 'DETAIL',
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      // Refresh workflows list
      fetchWorkflows();
      // Clear input
      setNewWorkflowUrl('');
    } catch (err) {
      console.error('Error adding workflow:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to add workflow';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="white">
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">Source Jobsites (Kadoa Imports)</Heading>
        <FormControl display="flex" alignItems="center" width="auto">
          <FormLabel htmlFor="kadoa-toggle" mb="0" mr={2}>
            Enable Kadoa
          </FormLabel>
          <Switch
            id="kadoa-toggle"
            isChecked={isKadoaEnabled}
            onChange={e => setIsKadoaEnabled(e.target.checked)}
          />
        </FormControl>
      </Flex>

      {!isKadoaEnabled ? (
        <Alert status="info" mb={4}>
          <AlertIcon />
          <AlertDescription>
            Kadoa integration is disabled. Enable it to import jobs from external job sites.
          </AlertDescription>
        </Alert>
      ) : (
        <>
          {/* Add new workflow form */}
          <Flex mb={4}>
            <InputGroup size="md">
              <Input
                placeholder="Enter job site URL (e.g., https://linkedin.com/jobs/...)"
                value={newWorkflowUrl}
                onChange={e => setNewWorkflowUrl(e.target.value)}
                isDisabled={isLoading}
              />
              <InputRightElement width="4.5rem">
                <Button
                  h="1.75rem"
                  size="sm"
                  onClick={addWorkflow}
                  isLoading={isLoading}
                  isDisabled={!newWorkflowUrl}
                >
                  Add
                </Button>
              </InputRightElement>
            </InputGroup>
          </Flex>

          {/* Error display */}
          {error && (
            <Alert status="error" mb={4}>
              <AlertIcon />
              <Box flex="1">
                <AlertTitle>Error</AlertTitle>
                <AlertDescription display="block">
                  {error}
                  {errorDetails && (
                    <Text mt={2} fontSize="sm" as="pre" whiteSpace="pre-wrap">
                      {errorDetails}
                    </Text>
                  )}
                </AlertDescription>
              </Box>
            </Alert>
          )}

          {/* Workflows list */}
          {isLoading && workflows.length === 0 ? (
            <Text>Loading workflows...</Text>
          ) : workflows.length > 0 ? (
            <>
              <Flex justify="space-between" align="center" mb={2}>
                <Text fontWeight="bold">Workflows</Text>
                <Text>Count: {workflows.length}</Text>
              </Flex>
              <List spacing={2}>
                {workflows.map((workflow, index) => (
                  <ListItem
                    key={workflow.id}
                    py={2}
                    borderBottomWidth={index !== workflows.length - 1 ? '1px' : 0}
                  >
                    <Flex justify="space-between" align="center">
                      <Box>
                        <Text as="span" mr={2}>
                          {String.fromCharCode(65 + index)} ({workflow.job_count || 0}):
                        </Text>
                        <Link href={workflow.url} color="blue.500" isExternal>
                          {workflow.url}
                        </Link>
                      </Box>
                      <Badge colorScheme={getStateBadgeColor(workflow.state)}>
                        State: {workflow.state}
                      </Badge>
                    </Flex>
                  </ListItem>
                ))}
              </List>
            </>
          ) : (
            !error && <Text>No workflows found. Add a URL to start scraping jobs.</Text>
          )}
        </>
      )}
    </Box>
  );
};

export default KadoaWorkflows;
