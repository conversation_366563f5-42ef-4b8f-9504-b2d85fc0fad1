'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Input,
  Checkbox,
  IconButton,
  HStack,
  VStack,
  FormControl,
  FormLabel,
  useToast,
  Badge,
  Text,
  Flex,
  useColorModeValue,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Alert,
  AlertIcon,
  Spinner,
} from '@chakra-ui/react';
import { FiPlus, FiEdit2, FiTrash2, FiRefreshCw } from 'react-icons/fi';
import { JobSource } from '../../../types';
import FirecrawlSettings from './FirecrawlSettings';

// Empty initial state for job sources
const initialJobSources: JobSource[] = [];

// Generate a three-letter prefix (AAA, AAB, AAC, etc.) from an ID
const generatePrefix = (id: number): string => {
  // Ensure ID is positive
  id = Math.max(0, id);

  // Convert to base-26 (A-Z) with 3 digits
  const firstChar = String.fromCharCode(65 + Math.floor(id / 676) % 26);  // 26^2 = 676
  const secondChar = String.fromCharCode(65 + Math.floor(id / 26) % 26);
  const thirdChar = String.fromCharCode(65 + id % 26);

  return `${firstChar}${secondChar}${thirdChar}`;
};

const URLManagement: React.FC = () => {
  const [jobSources, setJobSources] = useState<JobSource[]>(initialJobSources);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [newUrl, setNewUrl] = useState<string>('');
  const [newName, setNewName] = useState<string>('');
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [editingSource, setEditingSource] = useState<JobSource | null>(null);

  const toast = useToast();
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Function to fetch job URLs from the backend
  const fetchJobUrls = async (showToast = false) => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch job URLs from the backend
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/job-url-md`);

      if (!response.ok) {
        throw new Error(`Failed to fetch job URLs: ${response.status}`);
      }

      const data = await response.json();

      // Log the data for debugging
      console.log('Received job URLs with prefixes:', data);

      // Convert to JobSource format
      const sources: JobSource[] = data.map((item: any, index: number) => {
        // Generate prefix based on the ID (subtract 1 because IDs start at 1)
        const prefix = generatePrefix(parseInt(item.id) - 1);

        return {
          id: item.id,
          url: item.url,
          name: `Job Source ${item.id}`,
          is_active: item.is_active,
          job_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          prefix: prefix, // Always use locally generated prefix
        };
      });

      console.log('Setting job sources:', sources);
      setJobSources(sources);

      console.log(`Successfully loaded ${sources.length} job URLs from job_url.md`);

      if (showToast && sources.length > 0) {
        toast({
          title: 'Job URLs Loaded',
          description: `Successfully loaded ${sources.length} job URLs from job_url.md`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err) {
      console.error('Error fetching job URLs:', err);

      // Create a more user-friendly error message
      let errorMessage =
        'Failed to connect to the backend server. Please make sure the backend is running.';

      if (err instanceof Error) {
        if (err.message.includes('Failed to fetch')) {
          errorMessage =
            'Failed to connect to the backend server. Please make sure the backend is running on http://localhost:8000.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);

      // Use hardcoded data as fallback when backend is not available
      const fallbackSources: JobSource[] = [
        {
          id: '1',
          url: 'https://careers.eoexecutives.com/',
          name: 'Job Source 1',
          is_active: true,
          job_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          prefix: 'AAA',
        },
        {
          id: '2',
          url: 'https://mbmanagement.de/category/aktuelle-stellenangebote',
          name: 'Job Source 2',
          is_active: true,
          job_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          prefix: 'AAB',
        },
        {
          id: '3',
          url: 'https://vakanzen.drmaier-partner.de/stellenanzeigen/',
          name: 'Job Source 3',
          is_active: true,
          job_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          prefix: 'AAC',
        },
      ];

      console.log('Using fallback job sources:', fallbackSources);
      setJobSources(fallbackSources);

      toast({
        title: 'Using Fallback Data',
        description: 'Using fallback data since the backend is not available. ' + errorMessage,
        status: 'warning',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle refreshing job URLs
  const handleRefresh = () => {
    fetchJobUrls(true);
  };

  // Load job sources from the backend on component mount
  useEffect(() => {
    console.log('Component mounted, fetching job URLs...');
    fetchJobUrls();

    // Add a timer to fetch job URLs every 5 seconds for debugging
    const timer = setInterval(() => {
      console.log('Timer triggered, fetching job URLs...');
      fetchJobUrls();
    }, 5000);

    // Clean up the timer when the component unmounts
    return () => clearInterval(timer);
  }, []);

  // Handle adding a new URL
  const handleAddUrl = async () => {
    if (!newUrl) {
      toast({
        title: 'URL is required',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsLoading(true);

    try {
      // Call the backend API to add the URL to job_url.md
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/job-url-md`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: newUrl,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to add URL: ${response.status}`);
      }

      // Refresh the job URLs list
      const refreshResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/job-url-md`);

      if (!refreshResponse.ok) {
        throw new Error(`Failed to refresh job URLs: ${refreshResponse.status}`);
      }

      const data = await refreshResponse.json();

      // Convert to JobSource format
      const sources: JobSource[] = data.map((item: any, index: number) => {
        // Generate prefix based on the ID (subtract 1 because IDs start at 1)
        const prefix = generatePrefix(parseInt(item.id) - 1);

        return {
          id: item.id,
          url: item.url,
          name: `Job Source ${item.id}`,
          is_active: item.is_active,
          job_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          prefix: prefix, // Always use locally generated prefix
        };
      });

      setJobSources(sources);
      setNewUrl('');
      setNewName('');
      onClose();

      toast({
        title: 'URL added',
        description: 'The URL has been added successfully to job_url.md',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error adding URL:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to add URL';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle editing a URL
  const handleEditUrl = (source: JobSource) => {
    setEditingSource(source);
    setNewUrl(source.url);
    setNewName(source.name || '');
    onOpen();
  };

  // Handle saving edited URL
  const handleSaveEdit = () => {
    if (!editingSource) return;

    // In a real implementation, we would call the API
    const updatedSources = jobSources.map(source =>
      source.id === editingSource.id
        ? { ...source, url: newUrl, name: newName || newUrl, updated_at: new Date().toISOString() }
        : source
    );

    setJobSources(updatedSources);
    setEditingSource(null);
    setNewUrl('');
    setNewName('');
    onClose();

    toast({
      title: 'URL updated',
      description: 'The URL has been updated successfully',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  // Handle deleting URLs
  const handleDeleteUrls = async () => {
    if (selectedSources.length === 0) return;

    setIsLoading(true);
    setError(null);

    try {
      // Delete each selected URL from job_url.md
      for (const id of selectedSources) {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/job-url-md/${id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete URL with ID ${id}: ${response.status}`);
        }
      }

      // Refresh the job URLs list
      const refreshResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/job-url-md`);

      if (!refreshResponse.ok) {
        throw new Error(`Failed to refresh job URLs: ${refreshResponse.status}`);
      }

      const data = await refreshResponse.json();

      // Convert to JobSource format
      const sources: JobSource[] = data.map((item: any, index: number) => {
        // Generate prefix based on the ID (subtract 1 because IDs start at 1)
        const prefix = generatePrefix(parseInt(item.id) - 1);

        return {
          id: item.id,
          url: item.url,
          name: `Job Source ${item.id}`,
          is_active: item.is_active,
          job_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          prefix: prefix, // Always use locally generated prefix
        };
      });

      setJobSources(sources);
      setSelectedSources([]);

      toast({
        title: 'URLs deleted',
        description: `${selectedSources.length} URL(s) have been deleted from job_url.md`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Error deleting URLs:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete URLs';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle toggling active status
  const handleToggleActive = (id: string, isActive: boolean) => {
    // In a real implementation, we would call the API
    const updatedSources = jobSources.map(source =>
      source.id === id
        ? { ...source, is_active: isActive, updated_at: new Date().toISOString() }
        : source
    );

    setJobSources(updatedSources);

    toast({
      title: isActive ? 'URL activated' : 'URL deactivated',
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  // Handle selecting all sources
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedSources(jobSources.map(source => source.id));
    } else {
      setSelectedSources([]);
    }
  };

  // Handle selecting a single source
  const handleSelectSource = (id: string, isChecked: boolean) => {
    if (isChecked) {
      setSelectedSources([...selectedSources, id]);
    } else {
      setSelectedSources(selectedSources.filter(sourceId => sourceId !== id));
    }
  };

  // Debug: Log jobSources whenever it changes
  useEffect(() => {
    console.log('Current jobSources state:', jobSources);
  }, [jobSources]);

  // Handle Firecrawl scrape completion
  const handleScrapeComplete = () => {
    // Refresh the job URLs list
    fetchJobUrls(true);
  };

  return (
    <Box>
      <VStack spacing={4} align="stretch">
        {/* Firecrawl Settings */}
        <FirecrawlSettings onScrapeComplete={handleScrapeComplete} />

        <VStack spacing={2} align="stretch">
          {/* URL Management Controls */}
          <Flex justify="space-between" align="center" wrap="wrap" gap={1}>
            <Button leftIcon={<FiPlus />} colorScheme="blue" size="xs" onClick={onOpen}>
              Add URL
            </Button>

            <HStack spacing={1}>
              <Button
                leftIcon={<FiTrash2 />}
                colorScheme="red"
                variant="outline"
                size="xs"
                isDisabled={selectedSources.length === 0}
                onClick={handleDeleteUrls}
              >
                Delete Selected
              </Button>
              <Button
                leftIcon={<FiRefreshCw />}
                colorScheme="green"
                variant="outline"
                size="xs"
                isLoading={isLoading}
                onClick={handleRefresh}
              >
                Refresh
              </Button>
            </HStack>
          </Flex>

          {error && (
            <Alert status="error" mt={1} py={1} fontSize="xs">
              <AlertIcon boxSize="12px" />
              {error}
            </Alert>
          )}

        <Alert status="info" mt={1} py={1} fontSize="xs">
          <AlertIcon boxSize="12px" />
          <Box>
            <Text fontWeight="bold">Job Source Prefix System</Text>
            <Text>
              Each URL is assigned a unique three-letter prefix (AAA, AAB, AAC, etc.). Jobs from each source will be
              numbered sequentially (AAA1, AAA2, AAA3, etc.) when scraped. This system supports up to 17,576 different job sources.
            </Text>
          </Box>
        </Alert>

        {/* URL Table */}
        <Box borderWidth="1px" borderRadius="lg" borderColor={borderColor} overflow="hidden">
          {isLoading ? (
            <Flex justify="center" align="center" p={4}>
              <Spinner size="md" color="blue.500" />
            </Flex>
          ) : (
            <Table variant="simple" size="sm">
              <Thead>
                <Tr>
                  <Th width="20px" px={1} py={1}>
                    <Checkbox
                      size="sm"
                      isChecked={
                        selectedSources.length === jobSources.length && jobSources.length > 0
                      }
                      isIndeterminate={
                        selectedSources.length > 0 && selectedSources.length < jobSources.length
                      }
                      onChange={handleSelectAll}
                    />
                  </Th>
                  <Th width="60px" px={1} py={1}>Prefix</Th>
                  <Th width="80px" px={1} py={1}>Name</Th>
                  <Th px={1} py={1} width="350px">URL</Th>
                  <Th width="60px" px={1} py={1}>Status</Th>
                  <Th width="100px" px={1} py={1}>Last Scraped</Th>
                  <Th width="40px" px={1} py={1}>Jobs</Th>
                  <Th width="60px" px={1} py={1}>Actions</Th>
                </Tr>
              </Thead>
            <Tbody>
              {jobSources.length === 0 ? (
                <Tr>
                  <Td colSpan={8} textAlign="center" py={1}>
                    <Text fontSize="xs">No URLs found. Add a URL to get started.</Text>
                  </Td>
                </Tr>
              ) : (
                jobSources.map((source, index) => (
                  <Tr key={source.id || index}>
                    <Td px={1} py={1}>
                      <Checkbox
                        size="sm"
                        isChecked={selectedSources.includes(source.id)}
                        onChange={(e) => handleSelectSource(source.id, e.target.checked)}
                      />
                    </Td>
                    <Td px={1} py={1} fontSize="xs">
                      <Badge colorScheme="blue">{source.prefix || generatePrefix(parseInt(source.id) - 1)}</Badge>
                    </Td>
                    <Td px={1} py={1} fontSize="xs">
                      {source.name || `Source ${source.id}`}
                    </Td>
                    <Td px={1} py={1} fontSize="xs">
                      <Text noOfLines={1} title={source.url}>
                        {source.url}
                      </Text>
                    </Td>
                    <Td px={1} py={1} fontSize="xs">
                      <Badge colorScheme={source.is_active ? 'green' : 'gray'}>
                        {source.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </Td>
                    <Td px={1} py={1} fontSize="xs">
                      <Text>Never</Text>
                    </Td>
                    <Td px={1} py={1} fontSize="xs" textAlign="center">
                      {source.job_count || 0}
                    </Td>
                    <Td px={1} py={1}>
                      <HStack spacing={1}>
                        <IconButton
                          aria-label="Edit URL"
                          icon={<FiEdit2 />}
                          size="xs"
                          variant="ghost"
                          onClick={() => handleEditUrl(source)}
                        />
                        <IconButton
                          aria-label="Delete URL"
                          icon={<FiTrash2 />}
                          size="xs"
                          variant="ghost"
                          colorScheme="red"
                          onClick={() => {
                            setSelectedSources([source.id]);
                            handleDeleteUrls();
                          }}
                        />
                      </HStack>
                    </Td>
                  </Tr>
                ))
              )}
            </Tbody>
            </Table>
          )}
        </Box>
      </VStack>

      {/* Add/Edit URL Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="sm">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader fontSize="sm" py={2}>{editingSource ? 'Edit URL' : 'Add URL'}</ModalHeader>
          <ModalCloseButton size="sm" />
          <ModalBody pb={3} pt={0}>
            <VStack spacing={2}>
              <FormControl isRequired size="sm">
                <FormLabel fontSize="xs">URL</FormLabel>
                <Input
                  size="sm"
                  placeholder="https://example.com/jobs"
                  value={newUrl}
                  onChange={e => setNewUrl(e.target.value)}
                />
              </FormControl>
              <FormControl size="sm">
                <FormLabel fontSize="xs">Name (Optional)</FormLabel>
                <Input
                  size="sm"
                  placeholder="Example Jobs"
                  value={newName}
                  onChange={e => setNewName(e.target.value)}
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter py={2}>
            <Button variant="ghost" mr={2} size="xs" onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="blue" size="xs" onClick={editingSource ? handleSaveEdit : handleAddUrl}>
              {editingSource ? 'Save' : 'Add'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default URLManagement;
