# Backup of original run.ps1 - renamed to run_backup.ps1
#Requires -Version 5.1
<#
.SYNOPSIS
    Starts the JoMaDe application (FastAPI backend and Next.js frontend).
.DESCRIPTION
    This script performs the following actions:
    1. Sets up the environment by defining paths.
    2. Checks for and activates the Python virtual environment for the backend.
    3. Installs backend Python dependencies from requirements.txt if the venv was just created or if specified.
    4. Starts the FastAPI backend server (Uvicorn) in a new console window.
       - Attempts to use port 8000, then 8001 if 8000 is busy.
       - Includes a health check to ensure the FastAPI application loads correctly.
    5. Installs frontend Node.js dependencies using npm if node_modules is missing or if specified.
    6. Starts the Next.js frontend development server in a new console window.
       - Attempts to use port 3000, then 3001 if 3000 is busy.
       - Includes a health check and attempts to restart if it fails.
    7. Opens the application in the default web browser.
    8. Waits for the user to press Enter to stop all servers.
.PARAMETER ForceInstallBackendDeps
    Forces reinstallation of backend Python dependencies.
.PARAMETER ForceInstallFrontendDeps
    Forces reinstallation of frontend Node.js dependencies.
.EXAMPLE
    .\run.ps1
    Starts the application with default behavior.

.EXAMPLE
    .\run.ps1 -ForceInstallBackendDeps -ForceInstallFrontendDeps
    Starts the application and forces reinstallation of all dependencies.
#>
[CmdletBinding()]
param (
    [switch]$ForceInstallBackendDeps,
    [switch]$ForceInstallFrontendDeps
)

Clear-Host

Write-Host "🚀 Starting JoMaDe Application..." -ForegroundColor Cyan

# --- Configuration ---
$projectRoot = $PSScriptRoot
$backendDir = Join-Path $projectRoot "backend"
$frontendDir = Join-Path $projectRoot "frontend"
$venvName = ".venv" # Standard venv name
$venvPath = Join-Path $backendDir $venvName
$requirementsFile = Join-Path $backendDir "requirements.txt"

# --- Helper Functions ---

# Function to check if a port is in use
function Test-PortInUse {
    param (
        [int]$Port
    )
    try {
        $tcpConnection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        if ($tcpConnection) {
            return $true
        }
        return $false
    }
    catch {
        Write-Warning "Error checking port ${Port}: $($_.Exception.Message)"
        return $false # Assume not in use if check fails, to avoid blocking startup
    }
}

# Function to stop a process using a specific port
function Stop-ProcessUsingPort {
    param (
        [int]$Port
    )
    $processId = $null
    try {
        $connection = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($connection) {
            $processId = $connection.OwningProcess
            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "Stopping process '$($process.ProcessName)' (PID: $processId) using port $Port..." -ForegroundColor Yellow
                Stop-Process -Id $processId -Force -ErrorAction Stop
                Write-Host "Process (PID: $processId) stopped." -ForegroundColor Green
                return $true
            } else {
                Write-Warning "Could not find process with PID $processId that was using port $Port."
                return $false
            }
        } else {
            Write-Host "No process found using port $Port." -ForegroundColor DarkGray
            return $true # Effectively, port is clear
        }
    }
    catch {
        Write-Warning "Error stopping process (PID: $processId) on port ${Port}: $($_.Exception.Message)"
        return $false
    }
}

# Function to find an executable in PATH or common locations
function Find-Executable {
    param (
        [string]$CommandName,
        [string[]]$AdditionalPaths = @()
    )
    $executable = Get-Command $CommandName -ErrorAction SilentlyContinue
    if ($executable) {
        return $executable.Source
    }
    # Check common paths if not found in PATH
    $commonPaths = @(
        "$env:ProgramFiles\nodejs",
        "$env:ProgramFiles(x86)\nodejs",
        "$env:LOCALAPPDATA\Programs\Python\Python*\Scripts", # For python.exe from user install
        "$env:ProgramFiles\Python*\Scripts" # For python.exe from system install
    ) + $AdditionalPaths

    foreach ($path in $commonPaths) {
        $expandedPath = $ExecutionContext.SessionState.Path.GetUnresolvedProviderPathFromPSPath($path)
        if (Test-Path (Join-Path $expandedPath "$CommandName.exe")) {
            return (Join-Path $expandedPath "$CommandName.exe")
        }
        if (Test-Path (Join-Path $expandedPath $CommandName)) { # For non-.exe (e.g. bash scripts)
            return (Join-Path $expandedPath $CommandName)
        }
    }
    return $null
}


# --- Backend Setup ---
Write-Host "`n--- Backend Setup ---" -ForegroundColor Magenta

# 1. Python Virtual Environment
$venvPythonPath = Join-Path $venvPath "Scripts" "python.exe" # Common path for python.exe in venv
$venvPipPath = Join-Path $venvPath "Scripts" "pip.exe"

if (-not (Test-Path $venvPath)) {
    Write-Host "Python virtual environment not found at '$venvPath'. Creating..." -ForegroundColor Yellow
    $pythonExecutable = Find-Executable -CommandName "python"
    if (-not $pythonExecutable) {
        Write-Error "Python executable not found. Please ensure Python 3.7+ is installed and in your PATH."
        exit 1
    }
    Write-Host "Using Python: $pythonExecutable"
    Push-Location $backendDir
    try {
        & $pythonExecutable -m venv $venvName
        Write-Host "Virtual environment created." -ForegroundColor Green
        $ForceInstallBackendDeps = $true # Force install deps if venv was just created
    }
    catch {
        Write-Error "Failed to create virtual environment: $($_.Exception.Message)"
        exit 1
    }
    finally {
        Pop-Location
    }
} else {
    Write-Host "Python virtual environment found at '$venvPath'." -ForegroundColor Green
}

# 2. Install/Update Backend Dependencies
if ($ForceInstallBackendDeps -or (-not (Test-Path (Join-Path $venvPath "pyvenv.cfg")))) { # pyvenv.cfg is a good indicator of a valid venv
    Write-Host "Installing/updating backend Python dependencies from '$requirementsFile'..." -ForegroundColor Yellow
    if (-not (Test-Path $requirementsFile)) {
        Write-Warning "requirements.txt not found at '$requirementsFile'. Skipping dependency installation."
    } else {
        Push-Location $backendDir
        try {
            # Ensure pip is run with the venv's Python to avoid issues
            & $venvPythonPath -m pip install --upgrade pip
            & $venvPythonPath -m pip install -r $requirementsFile --log "pip_install.log"
            Write-Host "Backend dependencies installed/updated. See pip_install.log for details." -ForegroundColor Green
        }
        catch {
            Write-Error "Failed to install backend dependencies: $($_.Exception.Message). Check pip_install.log."
            exit 1
        }
        finally {
            Pop-Location
        }
    }
} else {
    Write-Host "Backend dependencies assumed to be up-to-date. Use -ForceInstallBackendDeps to reinstall." -ForegroundColor DarkGray
}

# 3. Start Backend Server
$backendProcess = $null
try {
    Write-Host 'Preparing to start FastAPI backend server...' -ForegroundColor Yellow

    # Determine backend port (8000 or 8001 if 8000 is in use)
    $backendPortToUse = 8000 # Default port
    if (Test-PortInUse -Port $backendPortToUse) {
        Write-Host "Port $backendPortToUse is already in use. Attempting to stop the process..." -ForegroundColor Yellow
        if (Stop-ProcessUsingPort -Port $backendPortToUse) {
            Write-Host "Successfully stopped process using port $backendPortToUse." -ForegroundColor Green
            Start-Sleep -Seconds 2
        } else {
            Write-Host "Could not stop process using port $backendPortToUse. Trying port 8001 instead." -ForegroundColor Yellow
            $backendPortToUse = 8001
            if (Test-PortInUse -Port $backendPortToUse) {
                Write-Host "Port $backendPortToUse is also in use. Attempting to stop the process..." -ForegroundColor Yellow
                 if (Stop-ProcessUsingPort -Port $backendPortToUse) {
                    Write-Host "Successfully stopped process using port $backendPortToUse." -ForegroundColor Green
                    Start-Sleep -Seconds 2
                 } else {
                    Write-Host "Could not stop process using port $backendPortToUse either. Backend startup might fail if Uvicorn cannot bind to this port." -ForegroundColor Red
                 }
            }
        }
    }
    # Assign to the script-scoped variable for later use (e.g. opening browser, messages)
    $script:backendPort = $backendPortToUse # $script: makes it accessible outside this try block if needed

    Write-Host "Attempting to start backend server on port $script:backendPort" -ForegroundColor Yellow
    Write-Host "Command: $venvPythonPath -m uvicorn api.main:app --reload --host 0.0.0.0 --port $script:backendPort --log-level debug" -ForegroundColor Yellow
    Write-Host "Working directory: $backendDir" -ForegroundColor Yellow

    Push-Location $backendDir
    try {
        # Start the process in a new window so we can see any errors
        $backendProcess = Start-Process -FilePath $venvPythonPath -ArgumentList "-m uvicorn api.main:app --reload --host 0.0.0.0 --port $script:backendPort --log-level debug" -WorkingDirectory $backendDir -PassThru -WindowStyle Normal

        Write-Host "Waiting for backend Uvicorn process to stabilize (5s)..." -ForegroundColor DarkGray
        Start-Sleep -Seconds 5 # Initial wait for Uvicorn process to start

        if ($null -eq $backendProcess -or $backendProcess.HasExited) {
            $exitCodeInfo = if ($backendProcess) { "with code $($backendProcess.ExitCode)" } else { "(process handle is null)" }
            throw "Backend Uvicorn server process exited prematurely $exitCodeInfo. Check the backend console window that may have briefly appeared or closed."
        }

        Write-Host "Backend Uvicorn server process (ID: $($backendProcess.Id)) is running. Verifying application health..." -ForegroundColor Yellow

        # Health check for the backend application
        $healthCheckUrl = "http://localhost:$($script:backendPort)/docs" # FastAPI /docs endpoint
        $maxRetries = 5    # Total attempts
        $retryDelaySeconds = 4 # Delay between retries
        $backendHealthy = $false

        for ($attempt = 1; $attempt -le $maxRetries; $attempt++) {
            Write-Host "Attempting backend health check $attempt of $maxRetries to $healthCheckUrl..." -ForegroundColor DarkGray
            try {
                # Use -TimeoutSec for Invoke-WebRequest, default is often too long
                $response = Invoke-WebRequest -Uri $healthCheckUrl -UseBasicParsing -TimeoutSec 7 -ErrorAction Stop
                if ($response.StatusCode -eq 200) {
                    Write-Host "Backend application is healthy (responded with 200 OK from $healthCheckUrl)." -ForegroundColor Green
                    $backendHealthy = $true
                    break # Exit loop on success
                } else {
                    # This case is less likely if -ErrorAction Stop is used for non-2xx status codes
                    Write-Host "Backend health check returned non-200 status: $($response.StatusCode) from $healthCheckUrl." -ForegroundColor Yellow
                }
            } catch {
                Write-Host "Backend health check attempt $attempt failed. Error: $($_.Exception.Message | Out-String -Width 200)" -ForegroundColor Yellow
                if ($_.Exception.ToString() -like "*No connection could be made because the target machine actively refused it*") {
                     Write-Host "This 'Connection refused' error often means the Uvicorn server process started, but the Python application (api.main) within it FAILED TO LOAD due to an internal error." -ForegroundColor Yellow
                     Write-Host "RECOMMENDATION: Check the backend server's console window (the one Uvicorn is running in) for Python tracebacks (e.g., 'ERROR: Error loading ASGI app. Could not import module api.main')." -ForegroundColor Red
                }
            }

            if (-not $backendHealthy -and $attempt -lt $maxRetries) {
                Write-Host "Waiting $retryDelaySeconds seconds before next health check attempt..." -ForegroundColor DarkGray
                Start-Sleep -Seconds $retryDelaySeconds
            }
        }

        if (-not $backendHealthy) {
            Write-Host "Backend application FAILED to become healthy after $maxRetries attempts at $healthCheckUrl." -ForegroundColor Red
            Write-Host "Please check the backend server's console window for detailed Python errors. The Uvicorn process might still be running but the app is broken." -ForegroundColor Red
            if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) { # Check if process exists before trying to stop
                Write-Host "Stopping unresponsive backend Uvicorn process (ID: $($backendProcess.Id))..." -ForegroundColor Yellow
                Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
            }
            throw "Backend application failed to start correctly and become healthy. Review logs in the Uvicorn console window."
        }

        Write-Host "✓ Backend server started successfully on port $script:backendPort and is healthy." -ForegroundColor Green

    }
    catch {
        # This catch is for errors during Start-Process, initial HasExited check, or the health check logic
        Write-Host "Error during backend server startup or health check: $($_.Exception.Message | Out-String -Width 200)" -ForegroundColor Red
        # The throw will propagate to the outer catch block
        throw $_
    }
    finally {
        Pop-Location # Corresponds to Push-Location $backendDir
    }
}
catch { # Outer catch for port conflicts or errors propagated from the inner try/catch
    Write-Host "CRITICAL ERROR: Backend setup or startup failed: $($_.Exception.Message | Out-String -Width 200)" -ForegroundColor Red
    # Ensure any potentially started backend process is stopped if we exit here
    if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) {
        Write-Host "Ensuring backend process (ID: $($backendProcess.Id)) is stopped due to critical error..." -ForegroundColor Yellow
        Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
    }
    exit 1
}


# --- Frontend Setup ---
Write-Host "`n--- Frontend Setup ---" -ForegroundColor Magenta
$npmPath = Find-Executable -CommandName "npm"
if (-not $npmPath) {
    Write-Error "npm (Node Package Manager) not found. Please ensure Node.js and npm are installed and in your PATH."
    # Attempt to stop backend if it started
    if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) { Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue }
    exit 1
}
Write-Host "Using npm: $npmPath"

# 1. Install/Update Frontend Dependencies
if ($ForceInstallFrontendDeps -or (-not (Test-Path (Join-Path $frontendDir "node_modules")))) {
    Write-Host "Installing/updating frontend Node.js dependencies in '$frontendDir'..." -ForegroundColor Yellow
    Push-Location $frontendDir
    try {
        & $npmPath install --legacy-peer-deps # Added --legacy-peer-deps as it's common for Next.js projects
        Write-Host "Frontend dependencies installed/updated." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to install frontend dependencies: $($_.Exception.Message)"
        # Attempt to stop backend if it started
        if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) { Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue }
        exit 1
    }
    finally {
        Pop-Location
    }
} else {
    Write-Host "Frontend dependencies (node_modules) exist. Use -ForceInstallFrontendDeps to reinstall." -ForegroundColor DarkGray
}

# 2. Start Frontend Server
$frontendProcess = $null
$frontendPortToUse = 3000 # Default Next.js port
try {
    Write-Host "Preparing to start Next.js frontend server..." -ForegroundColor Yellow
    if (Test-PortInUse -Port $frontendPortToUse) {
        Write-Host "Port $frontendPortToUse is already in use. Attempting to stop the process..." -ForegroundColor Yellow
        if (Stop-ProcessUsingPort -Port $frontendPortToUse) {
            Write-Host "Successfully stopped process using port $frontendPortToUse." -ForegroundColor Green
            Start-Sleep -Seconds 2
        } else {
            Write-Host "Could not stop process using port $frontendPortToUse. Trying port 3001 instead." -ForegroundColor Yellow
            $frontendPortToUse = 3001
            if (Test-PortInUse -Port $frontendPortToUse) {
                 Write-Host "Port $frontendPortToUse is also in use. Attempting to stop the process..." -ForegroundColor Yellow
                 if (Stop-ProcessUsingPort -Port $frontendPortToUse) {
                    Write-Host "Successfully stopped process using port $frontendPortToUse." -ForegroundColor Green
                    Start-Sleep -Seconds 2
                 } else {
                    Write-Host "Could not stop process using port $frontendPortToUse either. Frontend startup might fail." -ForegroundColor Red
                 }
            }
        }
    }
    $script:frontendPort = $frontendPortToUse # For messages and browser opening

    Write-Host "Attempting to start frontend server on port $script:frontendPort" -ForegroundColor Yellow
    Push-Location $frontendDir
    try {
        $frontendProcess = Start-Process -FilePath $npmPath -ArgumentList "run dev -- -p $script:frontendPort" -WorkingDirectory $frontendDir -PassThru -WindowStyle Normal
        Write-Host "Waiting for frontend server to become available (15s)..." -ForegroundColor DarkGray
        Start-Sleep -Seconds 15 # Give Next.js some time to compile and start

        # Frontend Health Check & Restart Logic
        $frontendHealthCheckUrl = "http://localhost:$($script:frontendPort)"
        $frontendMaxRetries = 2 # Total attempts including initial
        $frontendRetryDelaySeconds = 10
        $frontendHealthy = $false

        for ($attempt = 1; $attempt -le $frontendMaxRetries; $attempt++) {
            Write-Host "Attempting frontend health check $attempt of $frontendMaxRetries to $frontendHealthCheckUrl..." -ForegroundColor DarkGray
            try {
                $response = Invoke-WebRequest -Uri $frontendHealthCheckUrl -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
                if ($response.StatusCode -eq 200) {
                    Write-Host "Frontend server is healthy (responded with 200 OK from $frontendHealthCheckUrl)." -ForegroundColor Green
                    $frontendHealthy = $true
                    break
                }
            } catch {
                Write-Host "Frontend health check attempt $attempt failed: $($_.Exception.Message)" -ForegroundColor Yellow
            }

            if (-not $frontendHealthy -and $attempt -lt $frontendMaxRetries) {
                Write-Host "Frontend not healthy. Waiting $frontendRetryDelaySeconds seconds before next check..." -ForegroundColor DarkGray
                # Optional: attempt to restart frontend if it's consistently failing
                # if ($frontendProcess -and -not $frontendProcess.HasExited) {
                #     Write-Host "Attempting to restart frontend server..." -ForegroundColor Yellow
                #     Stop-Process -Id $frontendProcess.Id -Force -ErrorAction SilentlyContinue
                #     Start-Sleep -Seconds 3
                #     $frontendProcess = Start-Process -FilePath $npmPath -ArgumentList "run dev -- -p $script:frontendPort" -WorkingDirectory $frontendDir -PassThru -WindowStyle Normal
                #     Start-Sleep -Seconds 15 # Wait after restart
                # }
                Start-Sleep -Seconds $frontendRetryDelaySeconds
            }
        }

        if (-not $frontendHealthy) {
            Write-Warning "Frontend server at $frontendHealthCheckUrl did not become healthy after $frontendMaxRetries attempts."
            # Don't necessarily exit, user might want to debug, but backend is more critical
        } else {
             Write-Host "Frontend server started successfully on port $script:frontendPort and is healthy." -ForegroundColor Green
        }

    } catch {
        Write-Error "Error starting frontend server: $($_.Exception.Message)"
        # Attempt to stop backend if it started
        if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) { Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue }
        exit 1
    }
    finally {
        Pop-Location
    }

}
catch {
    Write-Error "CRITICAL ERROR during frontend setup or startup: $($_.Exception.Message)"
    if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) { Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue }
    if ($frontendProcess -and ($frontendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $frontendProcess.HasExited) { Stop-Process -Id $frontendProcess.Id -Force -ErrorAction SilentlyContinue }
    exit 1
}


# --- Application Running ---
Write-Host "`n--- Application Status ---" -ForegroundColor Magenta
Write-Host "Backend API should be running at: http://localhost:$($script:backendPort)/docs" -ForegroundColor Green
Write-Host "Frontend App should be running at: http://localhost:$($script:frontendPort)" -ForegroundColor Green

# Open in browser
$frontendUrl = "http://localhost:$($script:frontendPort)"
Write-Host "Opening $frontendUrl in your default browser..."
try {
    Start-Process $frontendUrl
}
catch {
    Write-Warning "Could not open URL in browser: $($_.Exception.Message)"
}

Write-Host "`nJoMaDe application startup sequence complete." -ForegroundColor Cyan
Write-Host "Backend and Frontend servers are running in separate console windows."
Write-Host "You can view their logs in those windows."
Write-Host "Backend API Docs: http://localhost:$($script:backendPort)/docs"
Write-Host "Frontend App:     http://localhost:$($script:frontendPort)"

# --- Wait for user to stop ---
Write-Host "`nPress Enter to stop servers..." -ForegroundColor Yellow
Read-Host

# --- Shutdown ---
Write-Host "`nStopping JoMaDe Application..." -ForegroundColor Cyan

if ($frontendProcess -and ($frontendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $frontendProcess.HasExited) {
    Write-Host "Stopping frontend server (PID: $($frontendProcess.Id))..."
    try {
        Stop-Process -Id $frontendProcess.Id -Force -ErrorAction Stop
        Write-Host "Frontend server stopped." -ForegroundColor Green
    }
    catch {
        Write-Warning "Could not stop frontend server (PID: $($frontendProcess.Id)): $($_.Exception.Message)"
    }
} else {
    Write-Host "Frontend server process not found or already stopped." -ForegroundColor DarkGray
}

if ($backendProcess -and ($backendProcess | Get-Process -ErrorAction SilentlyContinue) -and -not $backendProcess.HasExited) {
    Write-Host "Stopping backend server (PID: $($backendProcess.Id))..."
    try {
        Stop-Process -Id $backendProcess.Id -Force -ErrorAction Stop
        Write-Host "Backend server stopped." -ForegroundColor Green
    }
    catch {
        Write-Warning "Could not stop backend server (PID: $($backendProcess.Id)): $($_.Exception.Message)"
    }
} else {
    Write-Host "Backend server process not found or already stopped." -ForegroundColor DarkGray
}

Write-Host "JoMaDe has been stopped." -ForegroundColor Cyan
Write-Host "Press any key to exit..." -ForegroundColor Yellow
[void][System.Console]::ReadKey($true)
