import { api } from './api';
import { Workflow, Job } from '../types';

export interface WorkflowJobsResponse {
  workflow_id: string;
  total_jobs: number;
  jobs: Job[];
}

// Service class
export class KadoaService {
  private baseUrl: string = '/api/v1/kadoa';

  // List all workflows
  async listWorkflows(): Promise<Workflow[]> {
    return api.get<Workflow[]>(`${this.baseUrl}/workflows`);
  }

  // Create a new workflow
  async createWorkflow(url: string): Promise<Workflow> {
    return api.post<Workflow>(`${this.baseUrl}/workflows`, {
      url,
      schema_type: 'DETAIL',
    });
  }

  // Get workflow status
  async getWorkflowStatus(workflowId: string): Promise<Workflow> {
    return api.get<Workflow>(`${this.baseUrl}/workflows/${workflowId}`);
  }

  // Get jobs from a workflow
  async getWorkflowJobs(
    workflowId: string,
    page: number = 1,
    limit: number = 100
  ): Promise<WorkflowJobsResponse> {
    return api.get<WorkflowJobsResponse>(`${this.baseUrl}/workflows/${workflowId}/jobs`, {
      params: { page, limit },
    });
  }

  // Delete a workflow
  async deleteWorkflow(workflowId: string): Promise<void> {
    return api.delete<void>(`${this.baseUrl}/workflows/${workflowId}`);
  }

  // Pause a workflow
  async pauseWorkflow(workflowId: string): Promise<Workflow> {
    return api.post<Workflow>(`${this.baseUrl}/workflows/${workflowId}/pause`);
  }

  // Resume a workflow
  async resumeWorkflow(workflowId: string): Promise<Workflow> {
    return api.post<Workflow>(`${this.baseUrl}/workflows/${workflowId}/resume`);
  }

  // Get workflow statistics
  async getWorkflowStats(workflowId: string): Promise<any> {
    return api.get<any>(`${this.baseUrl}/workflows/${workflowId}/stats`);
  }
}
