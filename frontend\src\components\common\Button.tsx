'use client';

import React from 'react';
import {
  But<PERSON> as ChakraButton,
  ButtonProps as ChakraButtonProps,
  forwardRef,
  useColorModeValue,
  Icon,
  Flex,
} from '@chakra-ui/react';

export interface ButtonProps extends Omit<ChakraButtonProps, 'leftIcon' | 'rightIcon'> {
  leftIcon?: React.ReactElement | null | undefined;
  rightIcon?: React.ReactElement | null | undefined;
  isLoading?: boolean;
  loadingText?: string;
  variant?: 'solid' | 'outline' | 'ghost' | 'link' | 'subtle';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  colorScheme?: string;
  iconSpacing?: number;
}

const Button = forwardRef<ButtonProps, 'button'>((props, ref) => {
  const {
    children,
    leftIcon,
    rightIcon,
    isLoading,
    loadingText,
    variant = 'solid',
    size = 'md',
    colorScheme = 'blue',
    iconSpacing = 2,
    ...rest
  } = props;

  // Get color mode values outside of conditional
  const subtleBgLight = `${colorScheme}.50`;
  const subtleBgDark = `${colorScheme}.900`;
  const subtleColorLight = `${colorScheme}.600`;
  const subtleColorDark = `${colorScheme}.200`;
  const subtleHoverBgLight = `${colorScheme}.100`;
  const subtleHoverBgDark = `${colorScheme}.800`;
  const subtleActiveBgLight = `${colorScheme}.200`;
  const subtleActiveBgDark = `${colorScheme}.700`;

  // Use the hooks unconditionally
  const subtleBg = useColorModeValue(subtleBgLight, subtleBgDark);
  const subtleColor = useColorModeValue(subtleColorLight, subtleColorDark);
  const subtleHoverBg = useColorModeValue(subtleHoverBgLight, subtleHoverBgDark);
  const subtleActiveBg = useColorModeValue(subtleActiveBgLight, subtleActiveBgDark);

  // Custom styling for the subtle variant
  const subtleStyles = variant === 'subtle'
    ? {
        bg: subtleBg,
        color: subtleColor,
        _hover: {
          bg: subtleHoverBg,
        },
        _active: {
          bg: subtleActiveBg,
        },
      }
    : {};

  // Render left icon
  const renderLeftIcon = () => {
    if (isLoading) return null;
    if (!leftIcon) return null;

    if (typeof leftIcon === 'function') {
      return <Icon as={leftIcon} mr={iconSpacing} />;
    }

    return <Flex mr={iconSpacing}>{leftIcon}</Flex>;
  };

  // Render right icon
  const renderRightIcon = () => {
    if (isLoading) return null;
    if (!rightIcon) return null;

    if (typeof rightIcon === 'function') {
      return <Icon as={rightIcon} ml={iconSpacing} />;
    }

    return <Flex ml={iconSpacing}>{rightIcon}</Flex>;
  };

  return (
    <ChakraButton
      ref={ref}
      variant={variant === 'subtle' ? 'solid' : variant}
      size={size}
      colorScheme={colorScheme}
      isLoading={isLoading}
      loadingText={loadingText}
      {...subtleStyles}
      {...rest}
    >
      {renderLeftIcon()}
      {children}
      {renderRightIcon()}
    </ChakraButton>
  );
});

Button.displayName = 'Button';

export default Button;
