from typing import List
import logging
from fastapi import APIRouter, HTTPException
from ..core.config import settings
from ..schemas.kadoa import (
    WorkflowCreate,
    WorkflowResponse,
    JobResponse,
    WorkflowJobsResponse
)
from ..services.kadoa import KadoaService

router = APIRouter(prefix="/api/v1/kadoa", tags=["kadoa"])
logger = logging.getLogger(__name__)


@router.get("/workflows", response_model=List[WorkflowResponse])
async def list_workflows():
    """List all Kadoa workflows"""
    try:
        logger.info("Fetching workflows from Kadoa API")
        kadoa = KadoaService()
        workflows = await kadoa.list_workflows()
        logger.info(f"Successfully fetched {len(workflows)} workflows")
        return workflows
    except Exception as e:
        logger.error(f"Error fetching workflows: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/workflows", response_model=WorkflowResponse)
async def create_workflow(workflow: WorkflowCreate):
    """Create a new Kadoa workflow"""
    try:
        kadoa = KadoaService()
        new_workflow = await kadoa.create_workflow(workflow)
        return new_workflow
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workflows/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(workflow_id: str):
    """Get a specific Kadoa workflow"""
    try:
        kadoa = KadoaService()
        workflow = await kadoa.get_workflow_status(workflow_id)
        if not workflow:
            raise HTTPException(status_code=404, detail="Workflow not found")
        return workflow
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workflows/{workflow_id}/jobs", response_model=WorkflowJobsResponse)
async def get_workflow_jobs(
    workflow_id: str,
    page: int = 1,
    limit: int = 100
):
    """Get jobs from a workflow"""
    try:
        kadoa = KadoaService()
        jobs = await kadoa.get_workflow_jobs(workflow_id, page, limit)
        
        return WorkflowJobsResponse(
            workflow_id=workflow_id,
            total_jobs=len(jobs),
            jobs=jobs
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/workflows/{workflow_id}")
async def delete_workflow(workflow_id: str):
    """Delete a Kadoa workflow"""
    try:
        kadoa = KadoaService()
        await kadoa.delete_workflow(workflow_id)
        return {"message": "Workflow deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 