# JoMaDe Backend Startup Script
# This script starts the simplified JoMaDe backend server

Write-Host "Starting JoMaDe Backend Server..." -ForegroundColor Cyan

# Check if Python is installed
try {
    $pythonVersion = python --version
    Write-Host "Found Python: $pythonVersion" -ForegroundColor Green
}
catch {
    Write-Host "Error: Python not found. Please install Python 3.8 or higher." -ForegroundColor Red
    exit 1
}

# Define paths
$backendDir = Join-Path -Path (Get-Location) -ChildPath "backend"
$venvPath = Join-Path -Path $backendDir -ChildPath ".venv"
$venvPythonPath = Join-Path -Path $venvPath -ChildPath "Scripts\python.exe"

# Check if virtual environment exists
if (-not (Test-Path $venvPath)) {
    Write-Host "Creating virtual environment..." -ForegroundColor Yellow
    Push-Location $backendDir
    python -m venv .venv
    Pop-Location
}

# Check if requirements.txt exists
$requirementsPath = Join-Path -Path $backendDir -ChildPath "requirements.txt"
if (Test-Path $requirementsPath) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    & "$venvPythonPath" -m pip install -r $requirementsPath
}

# Check if port 8000 is in use
$portInUse = $null -ne (Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue)
if ($portInUse) {
    Write-Host "Port 8000 is already in use. Attempting to stop the process..." -ForegroundColor Yellow
    $connections = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue
    foreach ($conn in $connections) {
        if ($conn.State -eq 'Listen') {
            $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
            if ($null -ne $process) {
                Write-Host "Stopping process $($process.ProcessName) (ID: $($process.Id)) using port 8000" -ForegroundColor Yellow
                Stop-Process -Id $process.Id -Force
            }
        }
    }
    # Wait a moment for the port to be released
    Start-Sleep -Seconds 2
}

# Start the backend server
Write-Host "Starting backend server..." -ForegroundColor Green
Push-Location $backendDir
Write-Host "Running command: $venvPythonPath -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000" -ForegroundColor Yellow
& "$venvPythonPath" -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000

# This script will continue running until the user presses Ctrl+C
