'use client';

import React from 'react';
import {
  Box,
  VStack,
  Text,
  Flex,
  Icon,
  useColorModeValue,
  Divider,
  Tooltip,
} from '@chakra-ui/react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  FiHome,
  FiBriefcase,
  FiPieChart,
  FiSettings,
  FiDatabase,
  FiFileText,
  FiUser,
} from 'react-icons/fi';

interface NavItemProps {
  icon: any;
  children: string;
  path: string;
  isActive?: boolean;
  onClose?: () => void;
}

interface SidebarProps {
  isMobile?: boolean;
  onClose?: () => void;
}

const NavItem = ({ icon, children, path, isActive, onClose }: NavItemProps) => {
  const activeBg = useColorModeValue('blue.50', 'blue.900');
  const activeColor = useColorModeValue('blue.600', 'blue.200');
  const inactiveBg = useColorModeValue('transparent', 'transparent');
  const inactiveColor = useColorModeValue('gray.600', 'gray.400');

  return (
    <Link href={path} passHref>
      <Flex
        align="center"
        p="4"
        mx="4"
        borderRadius="lg"
        role="group"
        cursor="pointer"
        bg={isActive ? activeBg : inactiveBg}
        color={isActive ? activeColor : inactiveColor}
        fontWeight={isActive ? 'semibold' : 'normal'}
        _hover={{
          bg: activeBg,
          color: activeColor,
        }}
        onClick={onClose}
      >
        <Icon mr="4" fontSize="16" as={icon} />
        <Text fontSize="sm">{children}</Text>
        {isActive && (
          <Box
            position="absolute"
            right="0"
            width="4px"
            height="24px"
            bg="blue.500"
            borderRadius="4px 0 0 4px"
          />
        )}
      </Flex>
    </Link>
  );
};

const Sidebar: React.FC<SidebarProps> = ({ isMobile = false, onClose }) => {
  const pathname = usePathname();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  const navItems = [
    { name: 'Dashboard', icon: FiHome, path: '/dashboard' },
    { name: 'Jobs', icon: FiBriefcase, path: '/jobs' },
    { name: 'Analysis', icon: FiPieChart, path: '/analysis' },
    { name: 'Settings', icon: FiSettings, path: '/settings' },
  ];

  const settingsItems = [
    { name: 'URL Management', icon: FiDatabase, path: '/settings/urls' },
    { name: 'User Settings', icon: FiUser, path: '/settings/user' },
    { name: 'System Settings', icon: FiSettings, path: '/settings/system' },
  ];

  return (
    <Box
      position={isMobile ? 'relative' : 'fixed'}
      left="0"
      h="calc(100vh - 60px)"
      w="250px"
      bg={bgColor}
      borderRight="1px"
      borderRightColor={borderColor}
      pt="5"
      overflowY="auto"
    >
      <VStack spacing="1" align="stretch">
        {navItems.map(item => (
          <NavItem
            key={item.name}
            icon={item.icon}
            path={item.path}
            isActive={pathname === item.path || pathname?.startsWith(`${item.path}/`)}
            onClose={onClose}
          >
            {item.name}
          </NavItem>
        ))}

        <Divider my="4" />

        <Text
          px="8"
          fontSize="xs"
          fontWeight="semibold"
          textTransform="uppercase"
          color="gray.500"
          mb="2"
        >
          Settings
        </Text>

        {settingsItems.map(item => (
          <NavItem
            key={item.name}
            icon={item.icon}
            path={item.path}
            isActive={pathname === item.path}
            onClose={onClose}
          >
            {item.name}
          </NavItem>
        ))}
      </VStack>
    </Box>
  );
};

export default Sidebar;
